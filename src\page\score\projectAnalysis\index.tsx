import React, { useState, useEffect } from "react"
import { Breadcrumb, Table, Tabs } from "antd"
import type { TabsProps } from "antd"
import { useLocation, useNavigate } from "react-router-dom"
import { PMItem } from "../types"
import doneIcon from "@/assets/done.svg"
import icon1 from "@/assets/rankIcon.svg"
import icon2 from "@/assets/personIcon.svg"
import icon3 from "@/assets/reportIcon.svg"

interface UserRole {
  account: string
  userName: string
  code: string
  roleName: string
}

const ProjectAnalysis = () => {
  const navigate = useNavigate()
  const location = useLocation()
  const projectData: PMItem | undefined = location.state?.rowData
  const [userRoles, setUserRoles] = useState<UserRole[]>([])
  const [isAdmin, setIsAdmin] = useState(false)

  useEffect(() => {
    // 从localStorage获取用户角色信息
    const storedUserRoles = localStorage.getItem("userRoles")
    if (storedUserRoles) {
      try {
        const roles: UserRole[] = JSON.parse(storedUserRoles)
        setUserRoles(roles)
        // 检查是否为系统管理员
        const hasAdminRole = roles.some(role => role.code === "ADMIN")
        setIsAdmin(hasAdminRole)
      } catch (error) {
        console.error("解析用户角色信息失败:", error)
      }
    }
  }, [])

  const breadcrumbItems = [
    {
      title: (
        <span
          className="cursor-pointer hover:text-blue-500"
          onClick={() => navigate("/index/score")}
        >
          辅助评分
        </span>
      ),
    },
    {
      title: "项目分析",
    },
  ]

  const onChange = (key: string) => {
    console.log(key)
  }

  // 根据用户权限过滤标签页
  const items: TabsProps["items"] = [
    {
      key: "1",
      label: (
        <div className="flex items-center gap-2">
          <img src={icon1} alt="ranking" className="h-4 w-4" />
          参与者指标体系得分排名
        </div>
      ),
      children: (
        <div className="p-4">
          <h3 className="mb-4 text-lg font-semibold">参与者指标体系得分排名</h3>

          {/* 排名表格 */}
          <div className="mb-6 rounded-lg bg-gray-50 p-4">
            <h4 className="mb-3 font-medium text-gray-800">参与者得分排名</h4>
            <div className="space-y-2">
              {[
                { rank: 1, name: "张三", score: 95, department: "技术部" },
                { rank: 2, name: "李四", score: 88, department: "产品部" },
                { rank: 3, name: "王五", score: 82, department: "设计部" },
                { rank: 4, name: "赵六", score: 76, department: "测试部" },
              ].map(participant => (
                <div
                  key={participant.rank}
                  className="flex items-center justify-between rounded-md bg-white p-3 shadow-sm"
                >
                  <div className="flex items-center gap-3">
                    <div
                      className={`flex h-8 w-8 items-center justify-center rounded-full text-sm font-bold text-white ${
                        participant.rank === 1
                          ? "bg-yellow-500"
                          : participant.rank === 2
                            ? "bg-gray-400"
                            : participant.rank === 3
                              ? "bg-orange-400"
                              : "bg-blue-500"
                      }`}
                    >
                      {participant.rank}
                    </div>
                    <div>
                      <div className="font-medium text-gray-900">{participant.name}</div>
                      <div className="text-sm text-gray-500">{participant.department}</div>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-lg font-bold text-blue-600">{participant.score}</div>
                    <div className="text-xs text-gray-500">分</div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      ),
    },
    {
      key: "2",
      label: (
        <div className="flex items-center gap-2">
          <img src={icon2} alt="contribution" className="h-4 w-4" />
          个人参与者贡献度指标得分
        </div>
      ),
      children: (
        <div className="p-4">
          <div>
            <h3 className="mb-4 text-lg font-semibold">个人参与者贡献度指标得分</h3>
            {/* 名字 */}
          </div>

          <Table
            dataSource={[
              {
                key: "1",
                firstLevel: "技术能力",
                secondLevel: "编程技能",
                thirdLevel: "代码质量",
                totalScore: 20,
                personalScore: 18,
                firstLevelRowSpan: 6,
                secondLevelRowSpan: 3,
              },
              {
                key: "2",
                firstLevel: "",
                secondLevel: "",
                thirdLevel: "代码规范",
                totalScore: 15,
                personalScore: 14,
                firstLevelRowSpan: 0,
                secondLevelRowSpan: 0,
              },
              {
                key: "3",
                firstLevel: "",
                secondLevel: "",
                thirdLevel: "技术创新",
                totalScore: 15,
                personalScore: 13,
                firstLevelRowSpan: 0,
                secondLevelRowSpan: 0,
              },
              {
                key: "4",
                firstLevel: "",
                secondLevel: "问题解决",
                thirdLevel: "调试能力",
                totalScore: 20,
                personalScore: 19,
                firstLevelRowSpan: 0,
                secondLevelRowSpan: 3,
              },
              {
                key: "5",
                firstLevel: "",
                secondLevel: "",
                thirdLevel: "方案设计",
                totalScore: 15,
                personalScore: 16,
                firstLevelRowSpan: 0,
                secondLevelRowSpan: 0,
              },
              {
                key: "6",
                firstLevel: "",
                secondLevel: "",
                thirdLevel: "技术选型",
                totalScore: 15,
                personalScore: 14,
                firstLevelRowSpan: 0,
                secondLevelRowSpan: 0,
              },
              {
                key: "7",
                firstLevel: "协作能力",
                secondLevel: "沟通交流",
                thirdLevel: "需求理解",
                totalScore: 15,
                personalScore: 14,
                firstLevelRowSpan: 4,
                secondLevelRowSpan: 2,
              },
              {
                key: "8",
                firstLevel: "",
                secondLevel: "",
                thirdLevel: "表达能力",
                totalScore: 15,
                personalScore: 15,
                firstLevelRowSpan: 0,
                secondLevelRowSpan: 0,
              },
              {
                key: "9",
                firstLevel: "",
                secondLevel: "团队合作",
                thirdLevel: "协作配合",
                totalScore: 20,
                personalScore: 18,
                firstLevelRowSpan: 0,
                secondLevelRowSpan: 2,
              },
              {
                key: "10",
                firstLevel: "",
                secondLevel: "",
                thirdLevel: "知识分享",
                totalScore: 10,
                personalScore: 9,
                firstLevelRowSpan: 0,
                secondLevelRowSpan: 0,
              },
              {
                key: "11",
                firstLevel: "项目贡献",
                secondLevel: "任务完成",
                thirdLevel: "完成质量",
                totalScore: 25,
                personalScore: 23,
                firstLevelRowSpan: 3,
                secondLevelRowSpan: 2,
              },
              {
                key: "12",
                firstLevel: "",
                secondLevel: "",
                thirdLevel: "完成效率",
                totalScore: 20,
                personalScore: 19,
                firstLevelRowSpan: 0,
                secondLevelRowSpan: 0,
              },
              {
                key: "13",
                firstLevel: "",
                secondLevel: "文档维护",
                thirdLevel: "文档完整性",
                totalScore: 15,
                personalScore: 14,
                firstLevelRowSpan: 0,
                secondLevelRowSpan: 1,
              },
            ]}
            columns={[
              {
                title: "一级指标",
                dataIndex: "firstLevel",
                key: "firstLevel",
                width: 120,
                render: (text: string, record: any) => ({
                  children: text,
                  props: {
                    rowSpan: record.firstLevelRowSpan,
                  },
                }),
                className: "text-center font-medium",
              },
              {
                title: "二级指标",
                dataIndex: "secondLevel",
                key: "secondLevel",
                width: 120,
                render: (text: string, record: any) => ({
                  children: text,
                  props: {
                    rowSpan: record.secondLevelRowSpan,
                  },
                }),
                className: "text-center",
              },
              {
                title: "三级指标",
                dataIndex: "thirdLevel",
                key: "thirdLevel",
                width: 150,
                className: "text-center",
              },
              {
                title: "分值",
                dataIndex: "totalScore",
                key: "totalScore",
                width: 80,
                className: "text-center",
              },
              {
                title: "个人系数",
                dataIndex: "personalScore",
                key: "personalScore",
                width: 100,
                className: "text-center",
              },
            ]}
            pagination={false}
            bordered
            size="small"
            className="mb-4"
          />
        </div>
      ),
    },
    // 只有系统管理员才能看到评估报告生成标签页
    ...(isAdmin
      ? [
          {
            key: "3",
            label: (
              <div className="flex items-center gap-2">
                <img src={icon3} alt="report" className="h-4 w-4" />
                评估报告生成
              </div>
            ),
            children: (
              <div className="p-4">
                <h3 className="mb-4 text-lg font-semibold">评估报告生成</h3>

                {/* 报告生成选项 */}
                <div className="mb-6 rounded-lg bg-gray-50 p-4">
                  <h4 className="mb-3 font-medium text-gray-800">报告配置</h4>
                  <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                    <div className="rounded-md bg-white p-4 shadow-sm">
                      <h5 className="mb-2 font-medium text-gray-900">报告类型</h5>
                      <div className="space-y-2">
                        {[
                          { id: "summary", label: "项目总结报告", checked: true },
                          { id: "detailed", label: "详细分析报告", checked: true },
                          { id: "comparison", label: "对比分析报告", checked: false },
                        ].map(option => (
                          <label key={option.id} className="flex items-center gap-2">
                            <input
                              type="checkbox"
                              defaultChecked={option.checked}
                              className="rounded border-gray-300"
                            />
                            <span className="text-sm text-gray-700">{option.label}</span>
                          </label>
                        ))}
                      </div>
                    </div>

                    <div className="rounded-md bg-white p-4 shadow-sm">
                      <h5 className="mb-2 font-medium text-gray-900">包含内容</h5>
                      <div className="space-y-2">
                        {[
                          { id: "scores", label: "得分详情", checked: true },
                          { id: "charts", label: "图表分析", checked: true },
                          { id: "suggestions", label: "改进建议", checked: true },
                          { id: "timeline", label: "时间线分析", checked: false },
                        ].map(option => (
                          <label key={option.id} className="flex items-center gap-2">
                            <input
                              type="checkbox"
                              defaultChecked={option.checked}
                              className="rounded border-gray-300"
                            />
                            <span className="text-sm text-gray-700">{option.label}</span>
                          </label>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>

                {/* 报告预览 */}
                <div className="mb-6 rounded-lg bg-gray-50 p-4">
                  <h4 className="mb-3 font-medium text-gray-800">报告预览</h4>
                  <div className="rounded-md bg-white p-4 shadow-sm">
                    <div className="mb-4 border-b pb-2">
                      <h5 className="text-lg font-bold text-gray-900">
                        {projectData?.projectName || "项目名称"} - 评估报告
                      </h5>
                      <p className="text-sm text-gray-500">
                        生成时间: {new Date().toLocaleDateString("zh-CN")}
                      </p>
                    </div>

                    <div className="space-y-4">
                      <div>
                        <h6 className="mb-2 font-medium text-gray-800">项目概况</h6>
                        <div className="rounded bg-gray-50 p-3 text-sm text-gray-700">
                          <p>项目类型: {projectData?.projectType || "--"}</p>
                          <p>负责人: {projectData?.projectLeader || "--"}</p>
                          <p>参与人员: {projectData?.participants || "--"}</p>
                          <p>评估状态: 已完成</p>
                        </div>
                      </div>

                      <div>
                        <h6 className="mb-2 font-medium text-gray-800">评分汇总</h6>
                        <div className="rounded bg-gray-50 p-3">
                          <div className="grid grid-cols-2 gap-4 text-sm">
                            <div className="text-center">
                              <div className="text-2xl font-bold text-blue-600">95</div>
                              <div className="text-gray-600">平均得分</div>
                            </div>
                            <div className="text-center">
                              <div className="text-2xl font-bold text-green-600">4</div>
                              <div className="text-gray-600">参与人数</div>
                            </div>
                          </div>
                        </div>
                      </div>

                      <div>
                        <h6 className="mb-2 font-medium text-gray-800">关键指标</h6>
                        <div className="space-y-2">
                          {[
                            { name: "技术能力", score: 92, trend: "↗" },
                            { name: "协作能力", score: 88, trend: "↗" },
                            { name: "项目贡献", score: 95, trend: "→" },
                            { name: "学习成长", score: 85, trend: "↗" },
                          ].map(metric => (
                            <div
                              key={metric.name}
                              className="flex items-center justify-between rounded bg-gray-50 p-2 text-sm"
                            >
                              <span className="text-gray-700">{metric.name}</span>
                              <div className="flex items-center gap-2">
                                <span className="font-medium text-gray-900">{metric.score}</span>
                                <span className="text-lg">{metric.trend}</span>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* 生成按钮 - 只有系统管理员可见 */}
                {isAdmin && (
                  <div className="flex justify-center gap-4">
                    <button className="rounded-lg bg-blue-600 px-6 py-2 text-white hover:bg-blue-700">
                      生成完整报告
                    </button>
                    <button className="rounded-lg border border-gray-300 px-6 py-2 text-gray-700 hover:bg-gray-50">
                      导出全部
                    </button>
                    <button className="rounded-lg border border-gray-300 px-6 py-2 text-gray-700 hover:bg-gray-50">
                      导出评估报告
                    </button>
                  </div>
                )}
              </div>
            ),
          },
        ]
      : []),
  ]

  return (
    <div className="flex h-full min-h-screen flex-col gap-4 bg-[#F9FAFB] p-6">
      <div className="sticky top-[80px] z-10 flex h-[70px] items-center bg-[#F9FAFB]">
        <Breadcrumb items={breadcrumbItems} />
      </div>
      <div className="flex h-full flex-col gap-4 px-[80px]">
        <div className="flex items-center justify-between rounded-xl bg-white p-6 shadow-[0_1px_2px_0_rgba(0,0,0,0.05)]">
          <div className="flex flex-col">
            <span className="mb-4 text-[20px] font-bold text-[#1F2937]">
              {projectData?.projectName || "--"}
            </span>
            <div className="flex gap-4">
              <span className="text-[14px] text-[#1F2937]">
                项目类型：{projectData?.projectType || "--"}
              </span>
              <span className="text-[14px] text-[#1F2937]">
                负责人：{projectData?.projectLeader || "--"}
              </span>
              <span className="text-[14px] text-[#1F2937]">
                参与人：{projectData?.participants || "--"}
              </span>
              <span className="text-[14px] text-[#1F2937]">
                评估时间：{projectData?.endTime || "--"}
              </span>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <div className="flex items-center gap-2">
              <img src={doneIcon} alt="done" className="h-6 w-6" />
              <span className="text-[14px] font-medium text-[#10B981]">已完成</span>
            </div>
            {/* 只有系统管理员才能看到导出按钮 */}
            {isAdmin && (
              <div className="ml-4 flex items-center gap-2">
                <button className="rounded-lg border border-gray-300 px-4 py-1 text-sm text-gray-700 hover:bg-gray-50">
                  导出全部
                </button>
                <button className="rounded-lg border border-gray-300 px-4 py-1 text-sm text-gray-700 hover:bg-gray-50">
                  导出评估报告
                </button>
              </div>
            )}
          </div>
        </div>

        <div className="rounded-xl bg-white p-6 shadow-[0_1px_2px_0_rgba(0,0,0,0.05)]">
          <Tabs defaultActiveKey="1" items={items} onChange={onChange} />
        </div>
      </div>
    </div>
  )
}

export default ProjectAnalysis
