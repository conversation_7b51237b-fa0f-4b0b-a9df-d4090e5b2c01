import { message, Pagination, Table } from "antd"
import "../index.css"
import SearchFilter from "../components/SearchFilter"
import { useRef, useState, useEffect } from "react"
import { debounce } from "../../../utils/utils"
import { PMItem, SearchFilterState } from "../types"
import { useNavigate } from "react-router-dom"
import type { TableColumnsType, TableProps } from "antd"
import UploadDatasetModal from "../components/UploadDatasetModal"
import CommentModal from "../components/CommentModal"
import ActiveButton from "../components/ActiveButton"
import { getColumnsByRole } from "../components/TableColumns"
import { RoleCode, UserRole, UserRoleType, ProjectOperations } from "../types"
import { queryProjectList, batchDeleteProject, deleteProject } from "../../../api/score/dataset"

type TableRowSelection<T extends object = object> = TableProps<T>["rowSelection"]

const PMList = () => {
  const [uploadDatasetModal, setUploadDatasetModal] = useState(false)
  const [deleteModalVisible, setDeleteModalVisible] = useState(false)
  const [singleDeleteId, setSingleDeleteId] = useState<string | null>(null) // 单个删除的项目ID
  const tableRef = useRef(null)
  const [data, setData] = useState<PMItem[]>([])
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([])
  const [searchFilter, setSearchFilter] = useState<SearchFilterState>({
    filterAttribute: "projectName",
    sortAttribute: "createTime",
    sortDirection: undefined,
    filterTaskName: "",
    filterInput: "",
  })
  const [debouncedInput, setDebouncedInput] = useState("")
  const [loading, setLoading] = useState(false)
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  })

  const navigate = useNavigate()
  const debouncedSetFilterInput = useRef(
    debounce((value: string) => setDebouncedInput(value), 500)
  ).current

  // 从localStorage获取用户角色
  const getUserRole = (): RoleCode => {
    try {
      const userRoles = localStorage.getItem("userRoles")
      if (userRoles) {
        const roles: UserRole[] = JSON.parse(userRoles)
        if (roles.length > 0) {
          return roles[0].code as RoleCode
        }
      }
    } catch (error) {
      console.error("获取用户角色失败:", error)
    }
    return RoleCode.USER // 默认为普通用户
  }

  const userRole = getUserRole()

  // 项目操作函数
  const projectOperations: ProjectOperations = {
    onUploadScoringBasis: (id: string) => {
      console.log("上传评分依据:", id)
      // TODO: 实现上传评分依据逻辑
    },
    onPublish: (id: string) => {
      console.log("发布项目:", id)
      // TODO: 实现发布项目逻辑
    },
    onEdit: (id: string) => {
      console.log("编辑项目:", id)
      // TODO: 实现编辑项目逻辑
    },
    onDelete: async (id: string) => {
      setSingleDeleteId(id)
      setDeleteModalVisible(true)
    },
    onManualScore: (id: string) => {
      console.log("人工评分:", id)
      // TODO: 实现人工评分逻辑
    },
    onUploadMaterial: (id: string) => {
      console.log("上传材料:", id)
      // TODO: 实现上传材料逻辑
    },
    onTrace: (id: string) => {
      console.log("追溯:", id)
      // TODO: 实现追溯逻辑
    },
    onAnalyze: (id: string, rowData?: PMItem) => {
      navigate(`/index/score/projectAnalysis/${id}`, { state: { rowData } })
    },
  }

  // 将RoleCode转换为UserRoleType字符串
  const convertRoleCodeToUserRole = (roleCode: RoleCode): UserRoleType => {
    switch (roleCode) {
      case RoleCode.PROJECT_MANAGER:
        return "project_manager"
      case RoleCode.EVALUATOR:
        return "evaluator"
      case RoleCode.USER:
        return "user"
      case RoleCode.ADMIN:
        return "project_manager" // 管理员暂时使用项目负责人视图
      default:
        return "user"
    }
  }

  // 根据用户角色动态生成表格列
  const handleDetail = (id: string, rowData?: PMItem) => {
    navigate(`/index/score/project/${id}`, { state: { rowData } })
  }

  const columns: TableColumnsType<PMItem> = getColumnsByRole(
    convertRoleCodeToUserRole(userRole),
    projectOperations,
    handleDetail
  )

  // 获取项目列表数据
  const fetchProjectList = async () => {
    try {
      setLoading(true)
      const params = {
        current: pagination.current,
        size: pagination.pageSize,
        projectName:
          searchFilter.filterAttribute === "projectName" ? debouncedInput || undefined : undefined,
        projectType:
          searchFilter.filterAttribute === "projectType" ? debouncedInput || undefined : undefined,
        isScoreBasisUpload: searchFilter.sortDirection,
      }

      const res = await queryProjectList(params)
      // 确保响应数据格式正确
      if (res.data.code === 200) {
        setData(res.data.data.records || [])
        setPagination(prev => ({
          ...prev,
          total: res.data.data.total || 0,
        }))
      } else {
        throw new Error("响应数据格式错误")
      }
    } catch (error) {
      console.error("获取项目列表失败:", error)
      message.error("获取项目列表失败，请稍后重试")
      // 在错误情况下设置空数据
      setData([])
      setPagination(prev => ({
        ...prev,
        total: 0,
      }))
    } finally {
      setLoading(false)
    }
  }

  // 根据分页获取当前页数据
  const getCurrentPageData = () => {
    return data
  }

  useEffect(() => {
    fetchProjectList()
  }, [pagination.current, pagination.pageSize, debouncedInput, searchFilter.sortDirection])

  const handleFilterAttributeChange = (value: string) => {
    setSearchFilter(prev => ({ ...prev, filterAttribute: value }))
    // 重置分页到第一页
    setPagination(prev => ({ ...prev, current: 1 }))
  }
  const handleSortDirectionChange = (value: boolean) => {
    setSearchFilter(prev => ({ ...prev, sortDirection: value }))
    // 重置分页到第一页
    setPagination(prev => ({ ...prev, current: 1 }))
  }
  const handleSearch = () => {
    setSearchFilter(prev => ({ ...prev, filterTaskName: prev.filterInput }))
    // 重置分页到第一页
    setPagination(prev => ({ ...prev, current: 1 }))
  }
  const handleFilterInputChange = (value: string) => {
    setSearchFilter(prev => ({ ...prev, filterInput: value }))
    debouncedSetFilterInput(value)
    // 重置分页到第一页
    setPagination(prev => ({ ...prev, current: 1 }))
  }

  const onSelectChange = (newSelectedRowKeys: React.Key[]) => {
    console.log("selectedRowKeys changed: ", newSelectedRowKeys)
    setSelectedRowKeys(newSelectedRowKeys)
  }

  const rowSelection: TableRowSelection<PMItem> = {
    selectedRowKeys,
    onChange: onSelectChange,
  }
  const handleDeleteConfirm = async () => {
    try {
      setLoading(true)

      if (singleDeleteId) {
        // 单个删除
        await deleteProject(singleDeleteId)
        message.success("删除成功")
        setSingleDeleteId(null)
      } else {
        // 批量删除
        if (selectedRowKeys.length === 0) {
          message.error("请选择要删除的项目")
          return
        }
        await batchDeleteProject(selectedRowKeys as string[])
        message.success("删除成功")
        setSelectedRowKeys([])
      }

      setDeleteModalVisible(false)
      // 刷新列表
      await fetchProjectList()
    } catch (error) {
      console.error("删除项目失败:", error)
      message.error("删除项目失败，请稍后重试")
    } finally {
      setLoading(false)
    }
  }
  const handleDeleteCancel = () => {
    setDeleteModalVisible(false)
    setSingleDeleteId(null)
  }

  // 分页变化处理函数
  const handlePaginationChange = (page: number, pageSize?: number) => {
    setPagination(prev => ({
      ...prev,
      current: page,
      pageSize: pageSize || prev.pageSize,
    }))
  }

  return (
    <div className="pmList-container mb-8">
      <div className="mb-4 flex items-center justify-between">
        <div className="text-[20px] text-[#111]">项目管理列表</div>
      </div>

      <div className="pmList overflow-hidden rounded-3xl bg-white bg-gradient-to-b from-white to-white/40 px-[42px] py-[35px] shadow-[0_4px_4px_0_rgba(119,146,185,0.1)]">
        <div className="mb-3 flex justify-between">
          <SearchFilter
            filterAttribute={searchFilter.filterAttribute}
            sortDirection={searchFilter.sortDirection}
            filterInput={searchFilter.filterInput}
            onFilterAttributeChange={handleFilterAttributeChange}
            onSortDirectionChange={handleSortDirectionChange}
            onFilterInputChange={handleFilterInputChange}
            onSearch={handleSearch}
            type="pm"
          />
          <div className="flex items-center space-x-4">
            <ActiveButton
              selectedRowKeys={selectedRowKeys}
              onDeleteProject={() => setDeleteModalVisible(true)}
              onCreateProject={() => setUploadDatasetModal(true)}
            />
          </div>
        </div>
        <div>
          <Table
            ref={tableRef}
            loading={loading}
            columns={columns}
            dataSource={getCurrentPageData()}
            pagination={false}
            rowClassName="hover:bg-neutral-50"
            rowKey={record => record.id}
            rowSelection={rowSelection}
          />

          {/* 分页 */}
          <div className="flex items-center justify-end border-t border-neutral-200 bg-white px-4 py-3">
            <Pagination
              current={pagination.current}
              total={pagination.total}
              pageSize={pagination.pageSize}
              showSizeChanger
              showTotal={total => `共 ${total} 条记录`}
              onChange={handlePaginationChange}
              onShowSizeChange={handlePaginationChange}
            />
          </div>
        </div>
      </div>
      <UploadDatasetModal
        visible={uploadDatasetModal}
        OnClose={() => {
          setUploadDatasetModal(false)
          // 刷新列表数据
          fetchProjectList()
        }}
      ></UploadDatasetModal>
      <CommentModal
        visible={deleteModalVisible}
        onConfirm={handleDeleteConfirm}
        onCancel={handleDeleteCancel}
        content="删除后无法恢复请确认是否删除"
      />
    </div>
  )
}

export default PMList
