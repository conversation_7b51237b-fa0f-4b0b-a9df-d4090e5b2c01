import request from '../../services/request';
import {
  ApiResponse,
  LoginRequest,
  LoginResponse,
  PermissionsResponse,
  UserRole
} from '../../types';

// 登录API
export const loginApi = (data: LoginRequest): Promise<ApiResponse<LoginResponse>> => {
  return request.post('/login/login', data).then(res => res.data);
};

// 退出登录API
export const signOutApi = (): Promise<ApiResponse<null>> => {
  return request.get('/login/signOut').then(res => res.data);
};

// 获取当前用户权限API
export const getPermissionsApi = (): Promise<ApiResponse<PermissionsResponse>> => {
  return request.get('/login/hierarchicalPermissions').then(res => res.data);
};

// 获取当前用户角色API
export const getUserRolesApi = (): Promise<ApiResponse<UserRole[]>> => {
  return request.get('/login/roles').then(res => res.data);
};
