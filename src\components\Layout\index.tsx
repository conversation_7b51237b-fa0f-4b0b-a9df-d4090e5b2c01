import React, { useState, useEffect } from "react"
import { Layout } from "antd"
import { Outlet, useLocation } from "react-router-dom"
import Header from "../Header"
import { UserInfo } from "../../types"
import { useAuthStore } from "../../store/useAuthStore"
import "./index.css"

const { Content } = Layout

const AppLayout: React.FC = () => {
  const [userInfo, setUserInfo] = useState<UserInfo | null>(null)
  const location = useLocation()
  const { fetchPermissions, navItems, isAuthenticated } = useAuthStore()

  useEffect(() => {
    // 从localStorage获取用户信息
    const storedUserInfo = localStorage.getItem("userRoles")
    if (storedUserInfo) {
      try {
        setUserInfo(JSON.parse(storedUserInfo))
      } catch (error) {
        console.error("解析用户信息失败:", error)
      }
    }
  }, [])
  const hideHeaderPaths = ["/index/score/taskSetting"]

  const shouldShowHeader = !hideHeaderPaths.includes(location.pathname)
  useEffect(() => {
    // 如果用户已认证但没有权限数据，则获取权限数据
    const token = localStorage.getItem("token")
    if (token && navItems.length === 0) {
      fetchPermissions()
    }
  }, [fetchPermissions, navItems.length])

  return (
    <Layout className="app-layout">
      {shouldShowHeader && (
        <Header userInfo={userInfo || { id: "", userName: "用户", name: "用户" }} />
      )}
      <Content className="app-content with-header">
        <Outlet />
      </Content>
    </Layout>
  )
}

export default AppLayout
