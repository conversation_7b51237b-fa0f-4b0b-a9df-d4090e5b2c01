import React, { useState, useEffect } from "react"
import { Typography, Button, Row, Col } from "antd"
import {
  SafetyCertificateOutlined,
  DatabaseOutlined,
  QuestionCircleOutlined,
  FileTextOutlined,
  Bar<PERSON>hartOutlined,
  SettingOutlined,
  ProjectOutlined,
  RobotOutlined,
  <PERSON>wa<PERSON><PERSON>ightOutlined,
  ArrowRightOutlined,
} from "@ant-design/icons"
import { useNavigate } from "react-router-dom"
import { useAuthStore } from "../../store/useAuthStore"
import { Permission } from "../../types"
import "./index.css"
import { Spin } from "antd/lib"
import icon1 from "@/assets/icon1.svg"
import icon2 from "@/assets/icon2.svg"
import zskgl from "@/assets/zntgj.svg"
import znwd from "@/assets/znwd.svg"
import znppt from "@/assets/znppt.svg"
import fzpf from "@/assets/fzpf.svg"
import xmgl from "@/assets/xmgl.svg"
import zntgj from "@/assets/zntgj.svg"

const { Title, Paragraph } = Typography

const Home: React.FC = () => {
  const navigate = useNavigate()
  const { modulePermissions, loading, fetchPermissions } = useAuthStore()

  useEffect(() => {
    // 如果没有权限数据，则获取权限
    if (modulePermissions.length === 0) {
      fetchPermissions().then(() => {})
    }
  }, [])

  // 图标映射
  const iconMap: { [key: string]: React.ReactNode } = {
    ROLE_MANAGE: <img className="img1" src={icon2} alt="" />,
    DATA_MANAGE: <img className="img1" src={zskgl} alt="" />,
    INTELLIGENT_Q_AND_A: <img className="img1" src={znwd} alt="" />,
    INTELLIGENT_PPT_GENERATE: <img className="img1" src={znppt} alt="" />,
    AUXILIARY_SCORE: <img className="img1" src={fzpf} alt="" />,
    DATA_GOVERNANCE: <img className="img1" src={icon1} alt="" />,
    PROJECT_MANAGEMENT: <img className="img1" src={xmgl} alt="" />,
    AGENT_CONSTRUCTION: <img className="img1" src={zntgj} alt="" />,
  }

  // 颜色映射
  const colorMap: { [key: string]: string } = {
    ROLE_MANAGE: "#3B68E5",
    DATA_MANAGE: "#56B260",
    INTELLIGENT_Q_AND_A: "#8C3FE4",
    INTELLIGENT_PPT_GENERATE: "#DD682E",
    AUXILIARY_SCORE: "#D9AB3B",
    DATA_GOVERNANCE: "#5E5EE5",
    PROJECT_MANAGEMENT: "#4BA295",
    AGENT_CONSTRUCTION: "#D14685",
  }

  const getDescriptionByCode = (code: string): string => {
    const descriptions: { [key: string]: string } = {
      ROLE_MANAGE: "完整数据生命周期管理，核心技术可控，保障数据安全",
      DATA_MANAGE: "智能管理1000+文档，支持多种格式导入及高效检索",
      INTELLIGENT_Q_AND_A: "实时智能交互，精准理解用户意图提供专业答案",
      INTELLIGENT_PPT_GENERATE: "50+专业模板，支持智能内容生成及自动排版优化",
      AUXILIARY_SCORE: "3类评分标准，支持作业自动评分及智能反馈生成",
      DATA_GOVERNANCE: "数据清洗、质量检查及隐私保护的综合治理平台",
      PROJECT_MANAGEMENT: "项目规划、任务一体化管理，提升团队协作效率",
      AGENT_CONSTRUCTION: "智能体主动构建，工作流程自动化及智能决策支持",
    }
    return descriptions[code] || "系统功能模块"
  }

  const renderPermissionCard = (permission: Permission & { buttons?: Permission[] }) => {
    const icon = iconMap[permission.code] || <SettingOutlined />
    const color = colorMap[permission.code] || "#1890ff"
    return (
      <Col xs={24} sm={12} lg={8} key={permission.id}>
        <div className="h-full overflow-hidden rounded-xl border border-gray-200 bg-white shadow-md transition-all duration-200 hover:shadow-lg">
          <div className="flex h-full flex-col gap-5 p-6">
            {/* 图标 */}
            <div
              className="flex h-14 w-14 items-center justify-center rounded-lg"
              style={{ backgroundColor: `${color}`, color: "#fff" }}
            >
              <span className="text-2xl">{icon}</span>
            </div>

            {/* 标题 + 描述 */}
            <h3 className="text-2xl font-bold text-[#111]">{permission.name}</h3>
            <p className="text-sm text-[#4B5563]">{getDescriptionByCode(permission.code)}</p>

            {/* 按钮权限 */}
            <div className="space-y-2">
              <Button
                type="primary"
                className="enterBtn w-full"
                onClick={() => navigate(permission.url)}
              >
                进入管理后台
                <ArrowRightOutlined width={26} height={26} />
              </Button>
            </div>
          </div>
        </div>
      </Col>
    )
  }

  return (
    <div className="home-container container mx-auto flex-grow px-4 pt-8 md:px-6">
      <div className="mb-8 flex flex-col gap-5">
        <h3 className="text-secondary align-center flex justify-center">
          欢迎使用智能教学与科研工作平台
        </h3>
        <span className="text-tip align-center flex justify-center">
          推动院校教学与科研工作智能化，数字化高效发展
        </span>
      </div>
      <Row gutter={[24, 24]} className="px-32">
        {modulePermissions.map((permission: Permission) => renderPermissionCard(permission))}
      </Row>
      {loading && (
        <div className="mt-10 text-center">
          <Title level={4} className="flex h-[70vh] flex-col items-center justify-center gap-8">
            <Spin />
            加载中...
          </Title>
        </div>
      )}
      {modulePermissions.length === 0 && !loading && (
        <div className="empty-state mt-10 text-center">
          <Title level={4}>暂无可用功能</Title>
          <Paragraph>请联系管理员分配相应权限</Paragraph>
        </div>
      )}
    </div>
  )
}

export default Home
