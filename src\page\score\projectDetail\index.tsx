import React, { useState } from "react"
import { Breadcrumb, Table, Button, Input, message, Tag } from "antd"
import { useNavigate, useLocation } from "react-router-dom"
import { PMItem } from "../types"
import type { TableColumnsType } from "antd"
import { SettingOutlined, DeleteOutlined } from "@ant-design/icons"
import CommentModal from "../components/CommentModal"
import DescriptionModal from "../components/DescriptionModal"

// 子表数据类型
interface SubTaskType {
  key: React.Key
  taskName: string
  score: number
  participants: string
  description?: string
}

// 父表数据类型
interface ProjectStageType {
  key: React.Key
  stageOne: string
  stageTwo: string
  score: number
  participants: string
  subTasks?: SubTaskType[]
  description?: string
}

const ProjectDetail = () => {
  const navigate = useNavigate()
  const location = useLocation()

  // 获取传递的rowData
  const projectData: PMItem | undefined = location.state?.rowData

  // 编辑状态管理
  const [editingKey, setEditingKey] = useState<string>("")
  const [editingValue, setEditingValue] = useState<number>(0)

  // 删除确认弹窗状态
  const [deleteModalVisible, setDeleteModalVisible] = useState(false)
  const [deleteTaskInfo, setDeleteTaskInfo] = useState<{
    taskKey: string
    parentKey: string
    taskName: string
  } | null>(null)

  // 详情弹窗状态
  const [detailModalVisible, setDetailModalVisible] = useState(false)
  const [detailInfo, setDetailInfo] = useState<{
    title: string
    description: string
  } | null>(null)

  // 模拟数据
  const [dataSource, setDataSource] = useState<ProjectStageType[]>([
    {
      key: "1",
      stageOne: "需求分析",
      stageTwo: "需求调研",
      score: 100,
      participants: "张三, 李四",
      description: "项目需求分析阶段xxxxx",
      subTasks: [
        { key: "1-1", taskName: "用户需求收集", score: 40, participants: "张三" },
        { key: "1-2", taskName: "需求文档编写", score: 35, participants: "李四" },
        { key: "1-3", taskName: "需求评审", score: 25, participants: "张三, 李四" },
      ],
    },
    {
      key: "2",
      stageOne: "系统设计",
      stageTwo: "架构设计",
      score: 80,
      participants: "王五, 赵六",
      subTasks: [
        { key: "2-1", taskName: "系统架构设计", score: 50, participants: "王五" },
        { key: "2-2", taskName: "数据库设计", score: 30, participants: "赵六" },
      ],
    },
    {
      key: "3",
      stageOne: "开发实现",
      stageTwo: "编码开发",
      score: 120,
      participants: "孙七",
    },
  ])

  const breadcrumbItems = [
    {
      title: (
        <span
          className="cursor-pointer hover:text-blue-500"
          onClick={() => navigate("/index/score")}
        >
          辅助评分
        </span>
      ),
    },
    {
      title: "项目查看",
    },
  ]

  // 处理分数编辑
  const handleScoreEdit = (key: string, value: number, parentKey?: string) => {
    if (parentKey) {
      // 子表分数编辑
      const newDataSource = [...dataSource]
      const parentIndex = newDataSource.findIndex(item => item.key === parentKey)
      if (parentIndex !== -1 && newDataSource[parentIndex].subTasks) {
        const subTaskIndex = newDataSource[parentIndex].subTasks!.findIndex(
          item => item.key === key
        )
        if (subTaskIndex !== -1) {
          newDataSource[parentIndex].subTasks![subTaskIndex].score = value

          // 校验子表分值总和
          const totalSubScore = newDataSource[parentIndex].subTasks!.reduce(
            (sum, task) => sum + task.score,
            0
          )
          const parentScore = newDataSource[parentIndex].score

          if (totalSubScore !== parentScore) {
            message.error(`子表分值总和(${totalSubScore})必须等于父表分值(${parentScore})`)
            return
          }

          setDataSource(newDataSource)
          message.success("分值修改成功")
        }
      }
    } else {
      // 父表分数编辑
      const newDataSource = [...dataSource]
      const index = newDataSource.findIndex(item => item.key === key)
      if (index !== -1) {
        newDataSource[index].score = value
        setDataSource(newDataSource)
        message.success("分值修改成功")
      }
    }
    setEditingKey("")
  }

  // 删除子任务
  const handleDeleteSubTask = (key: string, parentKey: string) => {
    const newDataSource = [...dataSource]
    const parentIndex = newDataSource.findIndex(item => item.key === parentKey)
    if (parentIndex !== -1 && newDataSource[parentIndex].subTasks) {
      newDataSource[parentIndex].subTasks = newDataSource[parentIndex].subTasks!.filter(
        item => item.key !== key
      )
      setDataSource(newDataSource)
      message.success("删除成功")
    }
    setDeleteModalVisible(false)
    setDeleteTaskInfo(null)
  }

  // 显示删除确认弹窗
  const showDeleteModal = (taskKey: string, parentKey: string, taskName: string) => {
    setDeleteTaskInfo({ taskKey, parentKey, taskName })
    setDeleteModalVisible(true)
  }

  // 取消删除
  const handleCancelDelete = () => {
    setDeleteModalVisible(false)
    setDeleteTaskInfo(null)
  }

  // 显示详情弹窗
  const showDetailModal = (title: string, description: string) => {
    setDetailInfo({ title, description })
    setDetailModalVisible(true)
  }

  // 关闭详情弹窗
  const handleCloseDetail = () => {
    setDetailModalVisible(false)
    setDetailInfo(null)
  }

  // 任务配置（跳转到任务设置页面）
  const handleTaskConfig = (key: string) => {
    navigate("/index/score/taskSetting", { state: { key } })
  }

  // 父表列定义
  const columns: TableColumnsType<ProjectStageType> = [
    { title: "项目阶段一", dataIndex: "stageOne", key: "stageOne", width: 120 },
    {
      title: "项目阶段二",
      dataIndex: "stageTwo",
      key: "stageTwo",
      width: 120,
      render: (value: string, record: ProjectStageType) => {
        return (
          <span
            className="cursor-pointer text-[#1777FF]"
            onClick={() =>
              showDetailModal(`${record.stageTwo}描述`, record.description || "暂无描述")
            }
          >
            {value || "-"}
          </span>
        )
      },
    },
    {
      title: "分值",
      dataIndex: "score",
      key: "score",
      width: 120,
      render: (value: number, record: ProjectStageType) => {
        const isEditing = editingKey === record.key

        return isEditing ? (
          <Input
            type="number"
            defaultValue={value}
            autoFocus
            onBlur={e => {
              const newValue = parseInt(e.target.value) || 0
              handleScoreEdit(record.key as string, newValue)
            }}
            onPressEnter={e => {
              const newValue = parseInt((e.target as HTMLInputElement).value) || 0
              handleScoreEdit(record.key as string, newValue)
            }}
          />
        ) : (
          <span
            className="cursor-pointer rounded px-2 py-1 hover:bg-gray-100"
            onDoubleClick={() => {
              setEditingKey(record.key as string)
              setEditingValue(value)
            }}
          >
            {value}
          </span>
        )
      },
    },
    {
      title: "参与人",
      dataIndex: "participants",
      key: "participants",
      width: 150,
      render: (participants: string) => {
        if (!participants) return "-"
        return (
          <div className="flex flex-wrap gap-1">
            {participants.split(", ").map((participant, index) => (
              <Tag
                key={index}
                color="#C0E0FF"
                style={{ color: "#1F2937", border: "1px solid #C0E0FF" }}
              >
                {participant}
              </Tag>
            ))}
          </div>
        )
      },
    },
    {
      title: "操作",
      key: "operation",
      width: 120,
      render: (_, record: ProjectStageType) => (
        <div className="flex gap-2">
          <Button
            type="link"
            icon={<SettingOutlined />}
            size="small"
            onClick={() => handleTaskConfig(record.key as string)}
          >
            任务配置
          </Button>
        </div>
      ),
    },
  ]

  // 展开行渲染
  const expandedRowRender = (record: ProjectStageType) => (
    <Table<SubTaskType>
      columns={[
        { title: "", dataIndex: "", key: "empty1", width: 200 }, // 占位列
        { title: "", dataIndex: "", key: "empty2", width: 182 }, // 占位列
        {
          title: "任务名称",
          dataIndex: "taskName",
          key: "taskName",
          width: 200,
          render: (value: string, subRecord: SubTaskType) => {
            return (
              <span
                className="cursor-pointer text-[#1777FF]"
                onClick={() =>
                  showDetailModal(`${subRecord.taskName}描述`, subRecord.description || "暂无描述")
                }
              >
                {value || "-"}
              </span>
            )
          },
        },
        {
          title: "分值",
          dataIndex: "score",
          key: "score",
          width: 120,
          render: (value: number, subRecord: SubTaskType) => {
            const key = `${record.key}-${subRecord.key}`
            const isEditing = editingKey === key

            return isEditing ? (
              <Input
                type="number"
                defaultValue={value}
                autoFocus
                onBlur={e => {
                  const newValue = parseInt(e.target.value) || 0
                  handleScoreEdit(subRecord.key as string, newValue, record.key as string)
                }}
                onPressEnter={e => {
                  const newValue = parseInt((e.target as HTMLInputElement).value) || 0
                  handleScoreEdit(subRecord.key as string, newValue, record.key as string)
                }}
              />
            ) : (
              <span
                className="cursor-pointer rounded px-2 py-1 hover:bg-gray-100"
                onDoubleClick={() => {
                  setEditingKey(key)
                  setEditingValue(value)
                }}
              >
                {value}
              </span>
            )
          },
        },
        {
          title: "参与人",
          dataIndex: "participants",
          key: "participants",
          width: 150,
          render: (participants: string) => {
            if (!participants) return "-"
            return (
              <div className="flex flex-wrap gap-1">
                {participants.split(", ").map((participant, index) => (
                  <Tag
                    key={index}
                    color="#C0E0FF"
                    style={{ color: "#1F2937", border: "1px solid #C0E0FF" }}
                  >
                    {participant}
                  </Tag>
                ))}
              </div>
            )
          },
        },
        {
          title: "操作",
          key: "operation",
          width: 100,
          render: (_, subRecord: SubTaskType) => (
            <Button
              type="link"
              icon={<DeleteOutlined />}
              size="small"
              onClick={() =>
                showDeleteModal(subRecord.key as string, record.key as string, subRecord.taskName)
              }
            >
              删除
            </Button>
          ),
        },
      ]}
      dataSource={record.subTasks}
      pagination={false}
      showHeader={true}
    />
  )

  return (
    <div className="flex h-full flex-col gap-4 bg-[#F9FAFB] p-6">
      <div className="sticky top-[80px] z-10 flex h-[70px] items-center bg-[#F9FAFB]">
        <Breadcrumb items={breadcrumbItems} className="mb-6" />
      </div>

      <div className="rounded-[12px] bg-white p-6 shadow-[0_1px_2px_0_rgba(0,0,0,0.05)]">
        <span className="text-xl font-bold text-[#1F2937]">{projectData?.projectName || "-"}</span>
        <div className="mb-5 mt-4 flex gap-6 text-[#1F2937]">
          <span>项目类型：{projectData?.projectType || "-"}</span>
          <span>参与人：{projectData?.participants || "-"}</span>
          <span>评估时间：{projectData?.endTime || "-"}</span>
        </div>
      </div>
      <div className="rounded-[12px] bg-white p-6 shadow-[0_1px_2px_0_rgba(0,0,0,0.05)]">
        <span className="text-xl font-bold text-[#1F2937]">项目描述</span>
        <div className="mb-5 mt-4 flex gap-6 text-[#1F2937]">
          <span>{projectData?.projectDescription || "-"}</span>
        </div>
      </div>

      <div className="rounded-[12px] bg-white p-6 shadow-[0_1px_2px_0_rgba(0,0,0,0.05)]">
        <span className="mb-4 block text-lg font-bold text-[#1F2937]">项目阶段评分</span>
        <Table<ProjectStageType>
          columns={columns}
          dataSource={dataSource}
          expandable={{
            expandedRowRender,
            defaultExpandedRowKeys: ["1"],
            rowExpandable: record => !!(record.subTasks && record.subTasks.length > 0),
          }}
          pagination={false}
          summary={() => (
            <Table.Summary fixed>
              <Table.Summary.Row>
                <Table.Summary.Cell index={0}></Table.Summary.Cell>
                <Table.Summary.Cell index={1}>
                  <strong>汇总</strong>
                </Table.Summary.Cell>
                <Table.Summary.Cell index={2}></Table.Summary.Cell>
                <Table.Summary.Cell index={3}>
                  <strong>{dataSource.reduce((acc, cur) => acc + (cur.score || 0), 0)}</strong>
                </Table.Summary.Cell>
                <Table.Summary.Cell index={4}></Table.Summary.Cell>
              </Table.Summary.Row>
            </Table.Summary>
          )}
        />
      </div>
      <CommentModal
        visible={deleteModalVisible}
        onConfirm={() => {
          if (deleteTaskInfo) {
            handleDeleteSubTask(deleteTaskInfo.taskKey, deleteTaskInfo.parentKey)
          }
        }}
        onCancel={handleCancelDelete}
        content={`确定删除任务"${deleteTaskInfo?.taskName}"吗？删除后将无法恢复。`}
      />
      <DescriptionModal
        visible={detailModalVisible}
        title={detailInfo?.title}
        description={detailInfo?.description}
        onClose={handleCloseDetail}
        width={600}
      />
    </div>
  )
}

export default ProjectDetail
