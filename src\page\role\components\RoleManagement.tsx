import React, { useState, memo } from "react"
import {
  Table,
  Button,
  Modal,
  Form,
  Input,
  Tree,
  Space,
  Pagination,
  Popconfirm,
  message,
} from "antd"
import { PlusOutlined } from "@ant-design/icons"
import { PermissionTreeNode, Role } from "../../../types"

interface RoleManagementProps {
  roles: Role[]
  loading: boolean
  pagination: {
    currentPage: number
    total: number
    pageSize: number
  }
  setPagination: (pagination: any) => void
  permissionTree: PermissionTreeNode[]
  roleOptions: { value: number; label: string }[]
  getRoleList: () => void
  createRole: (params: any) => Promise<any>
  updateRole: (params: any) => Promise<any>
  deleteRole: (id: number) => Promise<any>
  fetchOptions: () => void
}

const RoleManagement: React.FC<RoleManagementProps> = ({
  roles,
  pagination,
  setPagination,
  permissionTree,
  getRoleList,
  createRole,
  updateRole,
  deleteRole,
  fetchOptions,
}) => {
  const [modalVisible, setModalVisible] = useState(false)
  const [editingRole, setEditingRole] = useState<Role | null>(null)
  const [form] = Form.useForm()
  const [checkedKeys, setCheckedKeys] = useState<string[]>([])
  const [expandedKeys, setExpandedKeys] = useState<string[]>([])

  // 初始化展开所有节点
  React.useEffect(() => {
    if (permissionTree.length > 0) {
      const allKeys = getAllKeys(permissionTree)
      setExpandedKeys(allKeys)
    }
  }, [permissionTree])

  // 递归获取所有节点的key
  const getAllKeys = (nodes: PermissionTreeNode[]): string[] => {
    const keys: string[] = []
    const traverse = (nodeList: PermissionTreeNode[]) => {
      nodeList.forEach(node => {
        keys.push(node.id.toString())
        if (node.pChild && node.pChild.length > 0) {
          traverse(node.pChild)
        }
      })
    }
    traverse(nodes)
    return keys
  }

  // 获取所有叶子节点 key（用于回显时只选叶子，避免父节点 id 导致整棵子树被选中）
  const getLeafKeys = (nodes: PermissionTreeNode[]): string[] => {
    const leafKeys: string[] = []
    const traverse = (list: PermissionTreeNode[]) => {
      list.forEach(node => {
        if (!node.pChild || node.pChild.length === 0) {
          leafKeys.push(node.id.toString())
        } else {
          traverse(node.pChild)
        }
      })
    }
    traverse(nodes)
    return leafKeys
  }

  // 转换权限树数据为Tree组件需要的格式
  const convertToTreeData = (nodes: PermissionTreeNode[]): any[] => {
    return nodes.map(node => ({
      title: node.label,
      key: node.id.toString(),
      children: node.pChild ? convertToTreeData(node.pChild) : undefined,
    }))
  }

  const handleOpenModal = (role: Role | null = null) => {
    setEditingRole(role)
    setModalVisible(true)

    if (role) {
      // 把原始数据放到表单（用于提交）
      form.setFieldsValue(role)

      // === 修复回显问题：只把叶子节点用于 Tree 的 checkedKeys 回显 ===
      const leafKeys = getLeafKeys(permissionTree)
      const rolePermissionIds: string[] = (role as any).permissionIds
        ? (role as any).permissionIds.map((x: any) => x.toString())
        : []
      // displayChecked 只包含那些确实是叶子的 id
      const displayChecked = rolePermissionIds.filter((k: string) => leafKeys.includes(k))
      setCheckedKeys(displayChecked)

      // 保证表单中 permissionIds 仍含原始（包含父级）以便提交时后端能按原设计解析
      form.setFieldsValue({ permissionIds: rolePermissionIds })
    } else {
      form.resetFields()
      setCheckedKeys([])
    }
  }

  const handleDelete = (key: number) => {
    deleteRole(key).then(response => {
      if (response.code === 200) {
        getRoleList()
        fetchOptions()
        message.success("删除成功")
      }
    })
  }

  const handleSubmit = () => {
    form.validateFields().then(values => {
      // 注意：onCheck 已经把 form.permissionIds 设置为 finalKeys（包含需要提交的父级）
      if (editingRole) {
        // === 关键修复：updateRole 需要带上 id ===
        const payload = { ...values, id: editingRole.id }
        updateRole(payload).then(response => {
          if (response.code === 200) {
            getRoleList()
            fetchOptions()
            message.success("编辑成功")
            setModalVisible(false) // 仅在成功后关闭
          } else {
            message.error(response?.message || "编辑失败")
          }
        })
      } else {
        createRole(values).then(response => {
          if (response.code === 200) {
            getRoleList()
            fetchOptions()
            message.success("创建成功")
            setModalVisible(false)
          } else {
            message.error(response?.message || "创建失败")
          }
        })
      }
    })
  }

  const columns = [
    {
      title: "角色名称",
      dataIndex: "name",
      render: (_: any, record: Role) => (
        <div>
          <p className="font-medium text-gray-800">{record.name}</p>
          <p className="text-xs text-gray-500">{record.description}</p>
        </div>
      ),
    },
    { title: "权限模块", dataIndex: "permissionModules" },
    { title: "用户数量", dataIndex: "userCount" },
    { title: "创建时间", dataIndex: "createTime" },
    {
      title: "操作",
      render: (_: any, record: Role) => (
        <Space>
          <Button type="link" onClick={() => handleOpenModal(record)}>
            编辑
          </Button>
          <Popconfirm title="确定删除该角色？" onConfirm={() => handleDelete(record.id)}>
            <Button type="link" danger>
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ]
  // 处理分页变化
  const handlePaginationChange = (page: number, pageSize?: number) => {
    setPagination({
      currentPage: page,
      pageSize: pageSize || pagination.pageSize,
      total: pagination.total,
    })
  }
  // 找到一个节点的父 id
  const findParentId = (nodes: PermissionTreeNode[], id: string): string | null => {
    for (const node of nodes) {
      if (node.id.toString() === id) {
        return node.pId && node.pId !== "0" ? node.pId : null
      }
      if (node.pChild && node.pChild.length > 0) {
        const res = findParentId(node.pChild, id)
        if (res) return res
      }
    }
    return null
  }

  // 判断某个父节点是否还有子节点被选中
  const hasAnyChildChecked = (
    nodes: PermissionTreeNode[],
    parentId: string,
    checkedKeys: string[]
  ): boolean => {
    const stack: PermissionTreeNode[] = [...nodes]
    while (stack.length) {
      const node = stack.pop()!
      if (node.pId === parentId && checkedKeys.includes(node.id.toString())) {
        return true
      }
      if (node.pChild) stack.push(...node.pChild)
    }
    return false
  }

  return (
    <div className="border-border card-shadow mb-8 rounded-xl border bg-white p-6 shadow-sm">
      <div className="mb-6 flex items-center justify-between">
        <h3 className="text-xl font-bold text-gray-800">用户角色管理</h3>
        <Button
          className="bg-primary hover:bg-primary/90 flex items-center rounded-lg px-4 py-2 text-sm font-medium text-white transition-colors duration-200"
          type="primary"
          icon={<PlusOutlined />}
          onClick={() => handleOpenModal()}
        >
          创建角色
        </Button>
      </div>

      <Table columns={columns} dataSource={roles} pagination={false} rowKey="key" />

      <div className="flex items-center justify-end border-t border-neutral-200 bg-white px-4 py-3">
        <Pagination
          current={pagination.currentPage}
          total={pagination.total}
          pageSize={pagination.pageSize}
          showSizeChanger
          showTotal={total => `共 ${total} 条记录`}
          onChange={handlePaginationChange}
          onShowSizeChange={handlePaginationChange}
        />
      </div>

      <Modal
        title={editingRole ? "编辑角色" : "创建角色"}
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        onOk={handleSubmit}
      >
        <Form layout="vertical" form={form}>
          <Form.Item
            label="角色名称"
            name="name"
            rules={[{ required: true, message: "请输入角色名称" }]}
          >
            <Input />
          </Form.Item>
          <Form.Item label="角色描述" name="description">
            <Input.TextArea rows={3} />
          </Form.Item>
          <Form.Item label="功能模块权限" name="permissionIds">
            <Tree
              checkable
              checkedKeys={checkedKeys}
              expandedKeys={expandedKeys}
              onCheck={checked => {
                const checkedKeysArray = Array.isArray(checked) ? checked : checked.checked
                setCheckedKeys(checkedKeysArray as string[])

                let finalKeys = [...checkedKeysArray]

                // 自动加上父级
                checkedKeysArray.forEach(key => {
                  const parentId = findParentId(permissionTree, key as string)
                  if (parentId && !finalKeys.includes(parentId)) {
                    finalKeys.push(parentId)
                  }
                })

                // 自动去掉没有子节点选中的父级
                finalKeys = finalKeys.filter(key => {
                  const parentId = findParentId(permissionTree, key as string)
                  if (!parentId) return true // 根节点保留
                  if (checkedKeysArray.includes(key)) return true // 本身被勾选
                  return hasAnyChildChecked(
                    permissionTree,
                    key as string,
                    checkedKeysArray as string[]
                  )
                })

                // 把要提交的 permissionIds 写回 form（包含必要的父级）
                form.setFieldsValue({ permissionIds: finalKeys })
              }}
              onExpand={expanded => {
                setExpandedKeys(expanded as string[])
              }}
              treeData={convertToTreeData(permissionTree)}
              style={{ maxHeight: 300, overflow: "auto" }}
            />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  )
}

export default memo(RoleManagement)
