import { BrowserRouter as Router, Routes, Route, Navigate } from "react-router-dom"
import { ConfigProvider } from "antd"
import zhCN from "antd/locale/zh_CN"
import Login from "./components/Login"
import Home from "./page/Home"
import AppLayout from "./components/Layout"
import AuthGuard from "./components/AuthGuard"
import IframePage from "./components/IframePage"
import Profile from "./page/Profile"
import "./App.css"
import React from "react"
import CreateScore from "./page/score/components/CreateScore"
import ProjectDetail from "./page/score/projectDetail"
import TaskSetting from "./page/score/taskSetting"
import ProjectAnalysis from "./page/score/projectAnalysis"
const ManagementPage = React.lazy(() => import("./page/role"))
const ScorePage = React.lazy(() => import("./page/score"))

function App() {
  return (
    <ConfigProvider locale={zhCN}>
      <Router>
        <Routes>
          {/* 登录页面 */}
          <Route path="/login" element={<Login />} />

          {/* 受保护的路由 */}
          <Route
            path="/"
            element={
              <AuthGuard>
                <AppLayout />
              </AuthGuard>
            }
          >
            {/* 首页 */}
            <Route index element={<Home />} />

            {/* 其他功能页面的路由可以在这里添加 */}
            <Route path="/index" element={<Home />} />
            <Route path="/index/role" element={<ManagementPage />} />
            <Route path="/index/data" element={<IframePage />} />
            <Route path="/index/qanda" element={<IframePage />} />
            <Route path="/index/ppt" element={<IframePage />} />
            <Route path="/index/score">
              <Route index element={<ScorePage />} />
              <Route path="create" element={<CreateScore />} />
              <Route path="project/:id" element={<ProjectDetail />} />
              <Route path="taskSetting" element={<TaskSetting />} />
              <Route path="projectAnalysis/:id" element={<ProjectAnalysis />} />
            </Route>
            <Route path="/index/governance" element={<IframePage />} />
            <Route path="/index/project" element={<div>项目管理页面</div>} />
            <Route path="/index/agentconstruction" element={<IframePage />} />

            {/* 个人信息页面 */}
            <Route path="/profile" element={<Profile />} />

            {/* iframe应用路由 */}
            <Route path="/app/:type" element={<IframePage />} />
          </Route>

          {/* 默认重定向到首页 */}
          <Route path="*" element={<Navigate to="/" replace />} />
        </Routes>
      </Router>
    </ConfigProvider>
  )
}

export default App
