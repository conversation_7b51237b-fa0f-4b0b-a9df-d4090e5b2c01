import React from 'react';
import { Modal } from 'antd';

interface DescriptionModalProps {
  visible: boolean;
  title?: string;
  description?: string;
  onClose: () => void;
  width?: number;
}

const DescriptionModal: React.FC<DescriptionModalProps> = ({
  visible,
  title,
  description,
  onClose,
  width = 600,
}) => {
  return (
    <Modal
      title={title}
      open={visible}
      onOk={onClose}
      onCancel={onClose}
      footer={null}
      width={width}
    >
      <p>{description || "暂无描述"}</p>
    </Modal>
  );
};

export default DescriptionModal;