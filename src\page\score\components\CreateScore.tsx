import React, { useState } from "react"
import { Form, Input, Select, DatePicker, Button, Modal, Table } from "antd"
import { SearchOutlined, FileWordOutlined, FilePdfOutlined, CloseOutlined } from "@ant-design/icons"
import PreviewFolderModal from "./PreviewFolderModal"
import { TableRowSelection } from "antd/es/table/interface"

const { TextArea } = Input

export default function CreateScore() {
  const [fileListModalVisible, setFileListModalVisible] = useState(false)
  const [filePreviewModalVisible, setFilePreviewModalVisible] = useState(false)
  const [previewTitle, setPreviewTitle] = useState("文件详情列表")
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([])
  const [loading, setLoading] = useState(false)

  // 文件选择表格数据
  const fileList = [
    {
      key: 1,
      name: "火灾决策规则",
      type: "系统文件",
      status: "---",
      icon: <FileWordOutlined className="mr-2 text-blue-600" />,
    },
    {
      key: 2,
      name: "供暖资源分配规则",
      type: "本地上传",
      status: "上传中 (40%)",
      icon: <FilePdfOutlined className="mr-2 text-red-600" />,
    },
    {
      key: 3,
      name: "火灾决策规则",
      type: "系统文件",
      status: "---",
      icon: <FileWordOutlined className="mr-2 text-blue-600" />,
    },
    {
      key: 4,
      name: "供暖资源分配规则",
      type: "本地上传",
      status: "上传中 (40%)",
      icon: <FilePdfOutlined className="mr-2 text-red-600" />,
    },
  ]

  const columns = [
    { title: "序号", dataIndex: "key", width: 60 },
    {
      title: "文件名称",
      dataIndex: "name",
      render: (text: string, record: any) => (
        <div className="flex items-center">
          {record.icon}
          {text}
        </div>
      ),
    },
    { title: "上传时间", dataIndex: "time" },
    {
      title: "操作",
      render: (_: any, record: any) => (
        <span
          className="text-primary cursor-pointer hover:underline"
          onClick={() => {
            setPreviewTitle(record.name)
            setFilePreviewModalVisible(true)
          }}
        >
          查看
        </span>
      ),
    },
  ]
  const onSelectChange = (newSelectedRowKeys: React.Key[]) => {
    console.log("selectedRowKeys changed: ", newSelectedRowKeys)
    setSelectedRowKeys(newSelectedRowKeys)
  }

  const rowSelection: TableRowSelection<any> = {
    selectedRowKeys,
    onChange: onSelectChange,
  }

  return (
    <div className="container mx-auto flex-grow px-4 py-6 md:px-6">
      <div className="mx-auto max-w-2xl rounded-lg bg-white p-6 shadow-sm">
        <Form layout="vertical" className="space-y-4">
          {/* 项目名称 */}
          <Form.Item label="项目名称" required>
            <Input placeholder="请输入项目名称" />
          </Form.Item>

          {/* 项目负责人 */}
          <Form.Item label="项目负责人" required>
            <Input placeholder="请输入负责人姓名" />
          </Form.Item>

          {/* 项目参与人员 */}
          <Form.Item label="项目参与人员">
            <Select
              mode="multiple"
              placeholder="点击选择参与人员"
              options={[
                { value: "张三", label: "张三" },
                { value: "李四", label: "李四" },
                { value: "王五", label: "王五" },
              ]}
            />
          </Form.Item>

          {/* 项目类型 */}
          <Form.Item label="项目类型" required>
            <Select
              placeholder="请选择项目类型"
              options={[
                { value: "research", label: "科研课题" },
                { value: "teaching", label: "教学成果" },
                { value: "course", label: "课程建设" },
                { value: "material", label: "教材" },
              ]}
            />
          </Form.Item>

          {/* 材料上传截止日期 */}
          <Form.Item label="材料上传截止日期">
            <DatePicker className="w-full" />
          </Form.Item>

          {/* 项目简介 */}
          <Form.Item label="项目简介">
            <TextArea rows={3} placeholder="请简要描述项目内容、目标和意义" />
          </Form.Item>

          {/* 文件选择 */}
          <Form.Item label="文件选择">
            <div className="flex gap-2">
              <Button type="primary">在系统文件夹中选择</Button>
              <Button type="primary">选择本地文件</Button>
              <Button
                onClick={() => setFileListModalVisible(true)}
                className="border border-gray-300 bg-gray-50 text-gray-500 hover:bg-gray-100"
              >
                文件查看
              </Button>
            </div>
          </Form.Item>

          {/* 操作按钮 */}
          <div className="flex justify-center gap-4 pt-4">
            <Button className="px-6" type="default">
              保存草稿
            </Button>
            <Button
              className="px-6"
              type="default"
              onClick={() => (window.location.href = "project-list.html")}
            >
              完成创建
            </Button>
          </div>
        </Form>
      </div>

      {/* 文件选择列表 Modal */}
      <Modal
        title="选择项目文件"
        open={fileListModalVisible}
        onCancel={() => setFileListModalVisible(false)}
        footer={[
          <Button key="confirm" type="primary" onClick={() => setFileListModalVisible(false)}>
            确认选择
          </Button>,
        ]}
        width={900}
      >
        {/* 搜索栏 */}
        <div className="mb-4 flex gap-4">
          <Select
            className="w-40"
            defaultValue="文件类型"
            options={[
              { value: "all", label: "文件类型" },
              { value: "pdf", label: "PDF" },
              { value: "word", label: "Word" },
              { value: "excel", label: "Excel" },
              { value: "ppt", label: "PPT" },
            ]}
          />
          <Input
            prefix={<SearchOutlined />}
            placeholder="请输入文件类型或名称"
            className="flex-1"
          />
        </div>

        {/* 文件表格 */}
        <Table
          rowKey="key"
          loading={loading}
          columns={columns}
          dataSource={fileList}
          pagination={{ pageSize: 5 }}
          rowSelection={rowSelection}
          rowClassName="hover:bg-gray-50"
        />
      </Modal>

      {/* 文件详情 Modal */}
      {/* <Modal
        title={previewTitle}
        open={filePreviewModalVisible}
        onCancel={() => setFilePreviewModalVisible(false)}
        footer={null}
        width={700}
      >
        <Table
          columns={[
            { title: "序号", dataIndex: "key", width: 60 },
            { title: "文件名称", dataIndex: "name" },
            { title: "文件大小", dataIndex: "size" },
            { title: "上传时间", dataIndex: "time" },
            {
              title: "操作",
              render: () => (
                <div className="flex gap-4">
                  <span className="text-primary cursor-pointer hover:underline">预览</span>
                  <span className="text-primary cursor-pointer hover:underline">下载</span>
                </div>
              ),
            },
          ]}
          dataSource={[
            { key: 1, name: "火灾决策规则.docx", size: "1.2MB", time: "2025-08-25 12:30" },
            { key: 2, name: "供暖资源分配规则.pdf", size: "800KB", time: "2025-08-25 12:40" },
          ]}
          pagination={false}
          rowClassName="hover:bg-gray-50"
        />
      </Modal> */}
      <PreviewFolderModal
        dataSet={[] as any}
        visible={filePreviewModalVisible}
        OnClose={() => setFilePreviewModalVisible(false)}
      />
    </div>
  )
}
