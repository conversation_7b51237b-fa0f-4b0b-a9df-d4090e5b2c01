import BarChart from "../components/BarChart"
import barbg from "@/assets/barbg.svg"
const PMDashboard = () => {
  return (
    <div className="mb-8">
      <div className="mb-4 flex items-center justify-between">
        <div className="text-[20px] text-[#111]">项目管理列表</div>
      </div>
      <div className="dashboard flex w-full gap-[70px] px-6 pb-2 pt-6">
        <div className="flex h-full !w-1/3 flex-col items-center justify-center gap-3">
          <div className="dashboardItem relative h-full w-full">
            <BarChart />
            <img
              src={barbg}
              alt=""
              className="absolute left-1/2 top-[74%] w-[81%] -translate-x-1/2 -translate-y-[52%] bg-cover"
            />
          </div>
          <div className="text-[#111]">项目状态查看图</div>
        </div>
        <div className="flex h-full !w-1/3 flex-col items-center justify-center gap-3">
          <div className="dashboardItem h-full">
            <div></div>
          </div>
          <div className="text-[#111]">项目文件上传数量</div>
        </div>
        <div className="flex h-full !w-1/3 flex-col items-center justify-center gap-3">
          <div className="dashboardItem h-full">
            <div></div>
          </div>
          <div className="text-[#111]">项目异常状态查看</div>
        </div>
      </div>
    </div>
  )
}

export default PMDashboard
