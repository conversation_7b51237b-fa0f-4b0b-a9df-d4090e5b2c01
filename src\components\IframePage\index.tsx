import React from "react"
import { useParams, useLocation } from "react-router-dom"
import { appConfigs, getAppTypeFromPath } from "../../config/appConfig"
import "./index.css"

interface IframePageProps {
  title?: string
  url?: string
}

const IframePage: React.FC<IframePageProps> = ({ title, url }) => {
  const { type } = useParams<{ type: string }>()
  const location = useLocation()

  const appType = getAppTypeFromPath(location.pathname) || type
  const currentConfig = appType ? appConfigs[appType] : null
  const pageTitle = title || currentConfig?.title || "应用页面"
  const pageUrl = url || currentConfig?.url || ""

  if (!pageUrl) {
    return (
      <div className="iframe-error">
        <h2>页面未找到</h2>
        <p>请检查URL配置</p>
      </div>
    )
  }

  return (
    <div className="iframe-container px-4 py-8 md:px-6">
      {/* <div className="iframe-header">
        <h1 className="iframe-title">{pageTitle}</h1>
      </div> */}
      <div className="iframe-content">
        <iframe
          src={pageUrl}
          title={pageTitle}
          className="iframe-frame"
          frameBorder="0"
          allowFullScreen
          sandbox="allow-same-origin allow-scripts allow-forms allow-popups allow-top-navigation"
        />
      </div>
    </div>
  )
}

export default IframePage
