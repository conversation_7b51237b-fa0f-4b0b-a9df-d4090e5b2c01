import { useState, useEffect, useRef } from "react"
import {
  queryR<PERSON><PERSON><PERSON>,
  getRoleOptions<PERSON><PERSON>,
  createR<PERSON><PERSON><PERSON>,
  updateRole<PERSON><PERSON>,
  deleteRole<PERSON><PERSON>,
} from "../api/role/RoleManagement"
import {
  queryUser<PERSON>pi,
  createUser<PERSON><PERSON>,
  deleteUser<PERSON><PERSON>,
  resetP<PERSON>word<PERSON><PERSON>,
  updateUser<PERSON><PERSON>,
  getDepartmentsA<PERSON>,
  getUnitsApi,
  toggleUserStatusApi,
  User,
  CreateUserParams,
  UpdateUserParams,
} from "../api/role/userManagement"
import {
  queryOperationLogApi,
  exportOperationLogApi,
  truncateAllOperationLogApi,
  getOperationTypesApi,
} from "../api/role/auditLog"
import { PermissionTreeNode, Role, OperationLog, OptionItem } from "../types"
import { getPermissionTreeApi } from "../api/role/permission"

// 自定义Hook，用于处理ManagementPage中的数据和接口调用
export const useManagementData = () => {
  // 用户管理相关状态
  const [users, setUsers] = useState<User[]>([])
  const [userLoading, setUserLoading] = useState(false)
  const [userPagination, setUserPagination] = useState({
    currentPage: 1,
    total: 0,
    pageSize: 10,
  })
  const [roleFilter, setRoleFilter] = useState<number | undefined>(undefined)
  const [unitFilter, setUnitFilter] = useState<string | undefined>(undefined)
  const [departmentFilter, setDepartmentFilter] = useState<string | undefined>(undefined)

  // 角色管理相关状态
  const [roles, setRoles] = useState<Role[]>([])
  const [roleLoading, setRoleLoading] = useState(false)
  const [rolePagination, setRolePagination] = useState({
    currentPage: 1,
    total: 0,
    pageSize: 10,
  })

  // 审计日志相关状态
  const [logs, setLogs] = useState<OperationLog[]>([])
  const [logLoading, setLogLoading] = useState(false)
  const [logPagination, setLogPagination] = useState({
    currentPage: 1,
    total: 0,
    pageSize: 10,
  })
  const [logSearch, setLogSearch] = useState("")
  const [operationTypes, setOperationTypes] = useState<{ value: number; label: string }[]>([])
  const [logFilters, setLogFilters] = useState({
    module: "",
    typeId: "",
    userName: "",
    startTime: "",
    endTime: "",
  })

  // 选项数据
  const [roleOptions, setRoleOptions] = useState<{ value: number; label: string }[]>([])
  const [departmentOptions, setDepartmentOptions] = useState<{ value: string; label: string }[]>([])
  const [unitOptions, setUnitOptions] = useState<{ value: string; label: string }[]>([])
  const [permissionTree, setPermissionTree] = useState<PermissionTreeNode[]>([])

  // 使用useRef来跟踪是否是第一次渲染
  const isFirstRender = useRef(true)

  // 获取选项数据
  const fetchOptions = async () => {
    try {
      const [rolesRes, departmentsRes, unitsRes] = await Promise.all([
        getRoleOptionsApi(),
        getDepartmentsApi(),
        getUnitsApi(),
      ])

      if (rolesRes.code === 200) {
        const options = rolesRes.data.map(role => ({
          value: role.id,
          label: role.name,
        }))

        setRoleOptions(options)
      }
      if (departmentsRes.code === 200) {
        const options = departmentsRes.data.map(department => ({
          value: department.key,
          label: department.detail,
        }))
        setDepartmentOptions(options)
      }
      if (unitsRes.code === 200) {
        const options = unitsRes.data.map(unit => ({
          value: unit.key,
          label: unit.detail,
        }))
        setUnitOptions(options)
      }
    } catch (error) {
      console.error("获取选项数据失败:", error)
    }
  }

  // 获取权限树
  const fetchPermissionTree = async () => {
    try {
      const response = await getPermissionTreeApi()
      if (response.code === 200) {
        setPermissionTree(response.data)
      }
    } catch (error) {
      console.error("获取权限树失败:", error)
    }
  }

  // 获取用户列表
  const getUserList = async () => {
    setUserLoading(true)
    try {
      const params = {
        current: userPagination.currentPage,
        size: userPagination.pageSize,
        roleId: roleFilter,
        unit: unitFilter,
        department: departmentFilter,
        pageable: true,
      }
      const response = await queryUserApi(params)
      if (response.code === 200) {
        setUsers(response.data.records)
        setUserPagination(prev => ({
          ...prev,
          total: response.data.total || 0,
          currentPage: response.data.current || prev.currentPage,
          pageSize: response.data.size || prev.pageSize,
        }))
      }
    } finally {
      setUserLoading(false)
    }
  }

  // 获取角色列表
  const getRoleList = async () => {
    setRoleLoading(true)
    try {
      const params = {
        current: rolePagination.currentPage,
        size: rolePagination.pageSize,
        pageable: true,
      }
      const response = await queryRoleApi(params)
      if (response.code === 200) {
        setRoles(response.data.records)
        setRolePagination(prev => ({
          ...prev,
          total: response.data.total || 0,
          currentPage: response.data.current || prev.currentPage,
          pageSize: response.data.size || prev.pageSize,
        }))
      }
    } catch (error) {
      console.error("获取角色列表失败:", error)
    } finally {
      setRoleLoading(false)
    }
  }

  // 获取操作日志列表
  const getLogList = async () => {
    setLogLoading(true)
    try {
      const params = {
        current: logPagination.currentPage,
        size: logPagination.pageSize,
        ...logFilters,
        userName: logSearch,
      }

      const response = await queryOperationLogApi(params)
      if (response.code === 200) {
        setLogs(response.data.records)
        setLogPagination(prev => ({
          ...prev,
          total: response.data.total || 0,
        }))
      }
    } catch (error) {
      console.error("获取操作日志失败:", error)
    } finally {
      setLogLoading(false)
    }
  }

  // 获取操作类型列表
  const fetchOperationTypes = async () => {
    try {
      const response = await getOperationTypesApi()
      if (response.code === 200) {
        setOperationTypes(
          response.data.map((item: any) => ({ value: item.key, label: item.detail }))
        )
      }
    } catch (error) {
      console.error("获取操作类型失败:", error)
    }
  }

  // 用户相关操作
  const createUser = async (params: CreateUserParams) => {
    return await createUserApi(params)
  }

  const updateUser = async (params: UpdateUserParams) => {
    return await updateUserApi(params)
  }

  const deleteUser = async (id: string) => {
    return await deleteUserApi(id)
  }

  const resetPassword = async (id: string) => {
    return await resetPasswordApi(id)
  }

  const toggleUserStatus = async (id: string) => {
    return await toggleUserStatusApi(id)
  }

  // 角色相关操作
  const createRole = async (params: any) => {
    return await createRoleApi(params)
  }

  const updateRole = async (params: any) => {
    return await updateRoleApi(params)
  }

  const deleteRole = async (id: number) => {
    return await deleteRoleApi(id)
  }

  // 日志相关操作
  const exportLog = async () => {
    return await exportOperationLogApi()
  }

  const truncateAllLogs = async () => {
    return await truncateAllOperationLogApi()
  }

  // 初始化数据
  useEffect(() => {
    // 避免在StrictMode下的重复调用
    // if (isFirstRender.current) {
    //   isFirstRender.current = false;
    //   return;
    // }

    // 初始化时获取所有必要的数据
    fetchOptions()
    fetchPermissionTree()
    fetchOperationTypes()
  }, [])

  // 用户列表依赖项变化时重新获取
  useEffect(() => {
    // if (!isFirstRender.current) {
    getUserList()
    // }
  }, [
    userPagination.currentPage,
    userPagination.pageSize,
    roleFilter,
    unitFilter,
    departmentFilter,
  ])

  // 角色列表依赖项变化时重新获取
  useEffect(() => {
    // if (!isFirstRender.current) {
    getRoleList()
    // }
  }, [rolePagination.currentPage, rolePagination.pageSize])

  // 日志列表依赖项变化时重新获取
  useEffect(() => {
    // if (!isFirstRender.current) {
    getLogList()
    // }
  }, [logPagination.currentPage, logPagination.pageSize, logFilters, logSearch])

  return {
    // 用户管理相关
    users,
    userLoading,
    userPagination,
    setUserPagination,
    roleFilter,
    setRoleFilter,
    unitFilter,
    setUnitFilter,
    departmentFilter,
    setDepartmentFilter,
    getUserList,
    createUser,
    updateUser,
    deleteUser,
    resetPassword,
    toggleUserStatus,

    // 角色管理相关
    roles,
    roleLoading,
    rolePagination,
    setRolePagination,
    getRoleList,
    createRole,
    updateRole,
    deleteRole,
    getRoleOptionsApi,
    fetchOptions,
    // 审计日志相关
    logs,
    logLoading,
    logPagination,
    setLogPagination,
    logSearch,
    setLogSearch,
    logFilters,
    setLogFilters,
    operationTypes,
    getLogList,
    exportLog,
    truncateAllLogs,

    // 选项数据
    roleOptions,
    departmentOptions,
    unitOptions,
    permissionTree,
  }
}