// 应用配置文件
// 定义各个应用的nginx代理路径映射

export interface AppConfig {
  title: string;
  url: string;
}

export interface AppConfigs {
  [key: string]: AppConfig;
}

// 获取当前环境的基础URL
const getBaseUrl = (): string => {
  // 在生产环境中，使用相对路径，nginx会处理代理
  if (process.env.NODE_ENV === 'production') {
    return '';
  }
  // 在开发环境中，直接返回空字符串，使用完整URL
  return '';
};

const baseUrl = getBaseUrl();

// 应用配置映射 - 根据环境使用不同的URL
export const appConfigs: AppConfigs = {
  kb: {
    title: "知识库管理",
    url: process.env.NODE_ENV === 'production' ? `${baseUrl}/api/kb/` : "http://123.57.244.236:18083/xchat-fe/manage/kb/",
  },
  chat: {
    title: "智能问答", 
    url: process.env.NODE_ENV === 'production' ? `${baseUrl}/api/chat/` : "http://123.57.244.236:18083/xchat-fe/welcome/",
  },
  ppt: {
    title: "智能PPT生成",
    url: process.env.NODE_ENV === 'production' ? `${baseUrl}/api/ppt/` : "http://xa1.puhuacloud.com:40610/",
  },
  data: {
    title: "数据治理",
    url: process.env.NODE_ENV === 'production' ? `${baseUrl}/api/data/` : "http://123.57.244.236:18005/files/",
  },
  agent: {
    title: "智能体构建",
    url: process.env.NODE_ENV === 'production' ? `${baseUrl}/api/agent/` : "http://123.57.244.236:18004/signin/",
  },
  governance: {
    title: "数据治理",
    url: process.env.NODE_ENV === 'production' ? `${baseUrl}/api/data/` : "http://123.57.244.236:18005/files/",
  }
};

// 根据路径自动识别应用类型的辅助函数
export const getAppTypeFromPath = (pathname: string): string | null => {
  if (pathname.includes("/data")) return "data";
  if (pathname.includes("/qanda")) return "chat";
  if (pathname.includes("/ppt")) return "ppt";
  if (pathname.includes("/governance")) return "governance";
  if (pathname.includes("/agentconstruction")) return "agent";
  return null;
};