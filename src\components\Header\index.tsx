import React, { useState, useEffect } from "react"
import { Layout, Avatar, Dropdown, Space, message } from "antd"
import { UserOutlined, LogoutOutlined, SettingOutlined, AppstoreOutlined } from "@ant-design/icons"
import { useNavigate, useLocation } from "react-router-dom"
import { signOutApi } from "../../api/role/auth"
import { useAuthStore } from "../../store/useAuthStore"
import { Permission, UserInfo } from "../../types"
import "./index.css"
import logo from "@/assets/logo.svg"

const { Header: AntHeader } = Layout

interface HeaderProps {
  userInfo?: UserInfo
}

const Header: React.FC<HeaderProps> = ({ userInfo }) => {
  const navigate = useNavigate()
  const location = useLocation()
  const { navItems, logout } = useAuthStore()
  const [activeKey, setActiveKey] = useState<string>("")

  useEffect(() => {
    // 根据当前路径设置激活的导航项
    const currentPath = location.pathname
    
    // 为子路由设置正确的激活状态
    // 如果当前路径是 /index/score/project/xxx，应该激活 /index/score
    let activeNavPath = currentPath
    if (currentPath.startsWith('/index/score/project/')) {
      activeNavPath = '/index/score'
    }
    
    setActiveKey(activeNavPath)
  }, [location.pathname])

  const handleLogout = async () => {
    try {
      await signOutApi()
      logout() // 使用Zustand store的logout方法
      message.success("退出登录成功")
      navigate("/login")
    } catch (error) {
      console.error("退出登录失败:", error)
      // 即使接口失败也清除本地数据
      logout()
      navigate("/login")
    }
  }

  const handleNavClick = (url: string) => {
    navigate(url)
  }

  const handleMenuClick = ({ key }: { key: string }) => {
    if (key === "profile") {
      navigate("/profile")
    }
  }

  const userMenuItems = [
    {
      key: "profile",
      icon: <UserOutlined />,
      label: "个人信息",
      onClick: () => navigate("/profile"),
    },
    {
      key: "settings",
      icon: <SettingOutlined />,
      label: "设置",
    },
    {
      type: "divider" as const,
    },
    {
      key: "logout",
      icon: <LogoutOutlined />,
      label: "退出登录",
      onClick: handleLogout,
    },
  ]

  return (
    <AntHeader className="app-header">
      <div className="flex w-full px-4 md:px-6">
        <div className="header-left">
          {/* 系统Logo和名称 */}
          <div className="system-logo" onClick={() => navigate("/")}>
            {/* <div className="logo-icon"> */}
            <img src={logo} alt="" />
            {/* </div> */}
            <h1 className="system-name">私有化大模型应用系统</h1>
          </div>

          {/* 导航菜单 */}
          <nav className="header-nav">
            {navItems.map((item: Permission) => (
              <a
                key={item.id}
                className={`nav-item ${activeKey === item.url ? "active" : ""}`}
                onClick={() => handleNavClick(item.url)}
              >
                {item.name}
                <span className="nav-underline"></span>
              </a>
            ))}
          </nav>
        </div>

        <div className="header-right">
          <Dropdown menu={{ items: userMenuItems }} placement="bottomRight" arrow>
            <Space className="user-info" style={{ cursor: "pointer" }}>
              {/* <Avatar src={userInfo?.[0]?.avatar} icon={<UserOutlined />} size="small" /> */}
              <span className="user-name">Hello, {userInfo?.[0]?.userName || "用户"}</span>
            </Space>
          </Dropdown>
        </div>
      </div>
    </AntHeader>
  )
}

export default Header
