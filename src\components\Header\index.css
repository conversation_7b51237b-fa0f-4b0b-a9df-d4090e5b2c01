.app-header {
  background: #fff;
  border-bottom: 1px solid #e5e7eb;
  box-shadow:
    0 1px 3px 0 rgba(0, 0, 0, 0.1),
    0 1px 2px 0 rgba(0, 0, 0, 0.06);
  padding: 0 1rem;
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  z-index: 50;
  height: 64px;
  line-height: 64px;
}

@media (min-width: 768px) {
  .app-header {
    padding: 0 1.5rem;
    height: 80px;
    /* line-height: 80px; */
  }
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 100%;
  max-width: 1200px;
  margin: 0 auto;
}

.header-left {
  display: flex;
  align-items: center;
  flex: 1;
}

/* 系统Logo区域 */
.system-logo {
  display: flex;
  align-items: center;
  cursor: pointer;
  margin-right: 2rem;
}

.logo-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 0.5rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  margin-right: 0.75rem;
  font-size: 1.25rem;
}

.system-name {
  color: #000;
  font-family: "HarmonyOS Sans SC";
  font-size: 28px;
  font-style: normal;
  font-weight: 700;
  line-height: normal;
  margin: 0;
  white-space: nowrap;
  margin-left: 20px;
}

@media (min-width: 768px) {
  .system-name {
    font-size: 1.5rem;
  }
}

/* 导航菜单 */
.header-nav {
  display: none;
  align-items: center;
  gap: 0.25rem;
}

@media (min-width: 768px) {
  .header-nav {
    display: flex;
    gap: 1rem;
  }
}

.nav-item {
  position: relative;
  display: inline-flex;
  align-items: center;
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  font-weight: 500;
  color: #6b7280;
  text-decoration: none;
  border-radius: 0.5rem;
  transition: all 0.3s ease;
  cursor: pointer;
}

.nav-item:hover {
  color: #667eea;
}

.nav-item.active {
  color: #667eea;
}

.nav-underline {
  position: absolute;
  bottom: 22px;
  left: 50%;
  width: 0;
  height: 2px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  transform: translateX(-50%);
  transition: width 0.3s ease;
}

.nav-item:hover .nav-underline,
.nav-item.active .nav-underline {
  width: 80%;
}

/* 用户区域 */
.header-right {
  display: flex;
  align-items: center;
}

.user-info {
  display: flex;
  align-items: center;
  padding: 0.25rem 0.5rem;
  border-radius: 0.5rem;
  transition: all 0.3s ease;
  cursor: pointer;
}

.user-name {
  font-size: 0.875rem;
  color: #1f2937;
  font-weight: 500;
  margin-left: 0.5rem;
}

/* 响应式设计 */
@media (max-width: 767px) {
  .system-logo {
    margin-right: 1rem;
  }

  .logo-icon {
    width: 2rem;
    height: 2rem;
    font-size: 1rem;
  }

  .system-name {
    font-size: 1rem;
  }

  .user-name {
    display: none;
  }
}
