import React, { useState } from "react"
import { Form, Input, <PERSON><PERSON>, Card, Tabs, message } from "antd"
import { useNavigate } from "react-router-dom"
import { loginApi } from "../../api/role/auth"
import { LoginRequest } from "../../types"
import { useAuthStore } from "../../store/useAuthStore"
import "./index.css"
import icon1 from "@/assets/icon1.svg"
import icon2 from "@/assets/icon2.svg"
import icon3 from "@/assets/icon3.svg"
import icon4 from "@/assets/icon4.svg"
import loginbg from "@/assets/loginbg.svg"
import passIcon from "@/assets/pass.svg"
import userIcon from "@/assets/user.svg"
import rightIcon from "@/assets/awr.svg"

const { TabPane } = Tabs

const Login: React.FC = () => {
  const [loading, setLoading] = useState(false)
  const [messageApi, contextHolder] = message.useMessage()
  const navigate = useNavigate()
  const { login, fetchPermissions, fetchUserRoles } = useAuthStore()

  const onFinish = async (values: LoginRequest) => {
    setLoading(true)
    try {
      const response = await loginApi(values)
      console.log(response, "response")
      if (response.code === 200) {
        // 创建用户信息
        const userInfo = {
          id: "1",
          userName: values.account,
          name: values.account,
        }

        // 使用Zustand store登录
        login(response.data, userInfo)

        // 获取权限和角色信息
        await Promise.all([fetchPermissions(), fetchUserRoles()])

        messageApi.success("登录成功")
        navigate("/")
      }
    } catch (error) {
      console.error("登录失败:", error)
      messageApi.error(error instanceof Error ? error.message : "登录失败")
    } finally {
      setLoading(false)
    }
  }

  const systemFeatures = [
    {
      icon: (
        <div className="icon icon1">
          <img className="img1" src={icon1} alt="" />
        </div>
      ),
      title: "国产自主",
      description: "完全自主研发、核心技术可控、保障数据安全",
    },
    {
      icon: (
        <div className="icon icon2">
          <img className="img1" src={icon2} alt="" />
        </div>
      ),
      title: "安全可控",
      description: "本地化部署、数据不出域，符合等保要求",
    },
    {
      icon: (
        <div className="icon icon3">
          <img className="img1" src={icon3} alt="" />
        </div>
      ),
      title: "技术领先",
      description: "基于国产大模型，持续技术创新引领行业发展",
    },
    {
      icon: (
        <div className="icon icon4">
          <img className="img1" src={icon4} alt="" />
        </div>
      ),
      title: "服务保障",
      description: "专业团队，本土化服务，长期稳定运行",
    },
  ]

  return (
    <>
      {contextHolder}
      <div className="login-container">
        <div className="login-content">
          {/* 左侧系统介绍 */}
          <div className="login-left">
            <div className="system-intro">
              <h1 className="system-title">
                私有化大模型 <span className="color1">应</span>
                <span className="color2">用</span>
                <span className="color3">系</span>
                <span className="color4">统</span>
              </h1>
              <p className="system-description">推动院校教学与科研工作智能化、数字化高效发展</p>
            </div>

            <div className="feature-cards">
              {systemFeatures.map((feature, index) => (
                <Card key={index} className="feature-card" hoverable>
                  <div className="feature-content">
                    <div className="feature-icon">{feature.icon}</div>

                    <h3 className="feature-title">{feature.title}</h3>
                    <p className="feature-description">{feature.description}</p>
                  </div>
                </Card>
              ))}
            </div>
          </div>

          {/* 右侧登录表单 */}
          <div className="login-right">
            <div className="login-form-container">
              <div className="login-header flex">
                <h2 className="welcome-title">欢迎登录</h2>
                <span className="line"></span>
              </div>

              <Tabs defaultActiveKey="account" className="login-tabs">
                <TabPane tab="账号登录" key="account">
                  <Form
                    name="login"
                    onFinish={onFinish}
                    layout={"vertical"}
                    autoComplete="off"
                    size="large"
                    requiredMark="optional"
                  >
                    <Form.Item
                      label="用户名"
                      name="account"
                      rules={[{ required: true, message: "请输入用户名!" }]}
                    >
                      <Input
                        prefix={<img src={userIcon} style={{ marginRight: "20px" }} alt="" />}
                        placeholder="请输入用户名"
                        className="login-input"
                      />
                    </Form.Item>

                    <Form.Item
                      name="password"
                      label="密码"
                      rules={[{ required: true, message: "请输入密码!" }]}
                    >
                      <Input.Password
                        prefix={<img src={passIcon} style={{ marginRight: "20px" }} alt="" />}
                        placeholder="密码"
                        className="login-input"
                      />
                    </Form.Item>

                    <Form.Item>
                      <Button
                        type="primary"
                        htmlType="submit"
                        loading={loading}
                        className="login-button"
                        block
                        icon={<img src={rightIcon} width={20} height={20} alt="" />}
                        iconPosition="end"
                      >
                        登录
                      </Button>
                    </Form.Item>
                  </Form>
                </TabPane>
              </Tabs>
            </div>
          </div>
        </div>
      </div>
    </>
  )
}

export default Login
