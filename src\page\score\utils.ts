export enum DatasetFileEnum {
  <PERSON><PERSON><PERSON>,
  Txt,
  <PERSON>,
  Pdf,
  Csv,
  Xlsx,
  <PERSON>son,
  Markdown,
  Image,
}
export interface ParseDatasetType {
  "fileId": string;
  "fileName": string;
  "fileType": string;
  "parseFilePath": string;
  "srcFilePath": string;
}
export interface DataSetType {
  id: string;
  name: string;
  taskName: string;
  datasetName: string;
  createTime: number;
  datasetStatus: string;
  progress: number;
  tags: string[];
  owner: string;
  // relatedQATaskList: RelatedTaskType[];
  childFiles: string[];
  complete: number;
  total: number;
  failedReason: string;
  tag: string;
  startTime: string;
  endTime: string;
}

export interface DataSetTreeType {
  id: string;
  name: string;
  status: string;
  qaCount?: number;
  fileName: string;
  fileId: string;
  fileType: number;
  srcFilePath: string;
  datasetName: string;
  parseFilePath: string;
  type: string; //ZIP DIR FILE
  children?: DataSetTreeType[];
  childFileId?: string;
  childFilePath?: string;
}

export const getFileType = (fileTypeStr: string) => {
  let fileType = 0;
  if (fileTypeStr === 'txt') {
    fileType = DatasetFileEnum.Txt;
  } else if (fileTypeStr === 'doc' || fileTypeStr === 'docx') {
    fileType = DatasetFileEnum.Doc;
  } else if (fileTypeStr === 'pdf') {
    fileType = DatasetFileEnum.Pdf;
  } else if (fileTypeStr === 'csv') {
    fileType = DatasetFileEnum.Csv;
  } else if (fileTypeStr === 'xlsx') {
    fileType = DatasetFileEnum.Xlsx;
  } else if (fileTypeStr === 'json') {
    fileType = DatasetFileEnum.Json;
  } else if (fileTypeStr === 'md') {
    fileType = DatasetFileEnum.Markdown;
  } else if (fileTypeStr === 'png' || fileTypeStr === 'jpg' || fileTypeStr === 'jpeg') {
    fileType = DatasetFileEnum.Image;
  }
  return fileType;
};