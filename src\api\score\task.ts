import request from "../../services/request"

export interface deleteillationTaskType {
  taskId: string
  fileIdList: string[]
}
export interface qATaskRetryRequest {
  taskId: string
  fileIdList: string[]
}

/**
 * 删除错误任务
 * @param param 需要删除错误任务
 * @returns
 */
export function deleteillationTask(param: deleteillationTaskType) {
  const url = `/task/problem`
  return request.delete(url, { data: param })
}

/**
 * 重新推理
 * @param param 需要推理解析任务
 * @returns
 */
export function reillationTask(param: qATaskRetryRequest) {
  const url = `/task/problem`
  return request.post(url, param)
}
