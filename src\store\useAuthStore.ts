import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { Permission, UserRole, UserInfo } from '../types';
import { getPermissionsApi, getUserRolesApi } from '../api/role/auth';

interface AuthState {
  // 状态
  isAuthenticated: boolean;
  userInfo: UserInfo | null;
  userRoles: UserRole[];
  permissions: Permission[];
  loading: boolean;
  
  // 计算属性
  modulePermissions: Permission[]; // type=1的权限（功能模块）
  buttonPermissions: Permission[]; // type=2的权限（按钮权限）
  navItems: Permission[]; // 有对应按钮权限的导航项
  
  // 方法
  login: (token: string, userInfo: UserInfo) => void;
  logout: () => void;
  fetchPermissions: () => Promise<void>;
  fetchUserRoles: () => Promise<void>;
  setLoading: (loading: boolean) => void;
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      // 初始状态
      isAuthenticated: false,
      userInfo: null,
      userRoles: [],
      permissions: [],
      loading: false,
      modulePermissions: [],
      buttonPermissions: [],
      navItems: [],

      // 登录
      login: (token: string, userInfo: UserInfo) => {
        localStorage.setItem('token', token);
        set({
          isAuthenticated: true,
          userInfo,
        });
      },

      // 退出登录
      logout: () => {
        localStorage.removeItem('token');
        localStorage.removeItem('userInfo');
        localStorage.removeItem('userRoles');
        set({
          isAuthenticated: false,
          userInfo: null,
          userRoles: [],
          permissions: [],
          modulePermissions: [],
          buttonPermissions: [],
          navItems: [],
        });
      },

      // 获取权限数据
      fetchPermissions: async () => {
        const { setLoading } = get();
        setLoading(true);
        
        try {
          const response = await getPermissionsApi();
          if (response.code === 200) {
            const systemData = response.data[0];
            const allPermissions = systemData?.children || [];
            
            // 分离模块权限和按钮权限
            const modulePermissions = allPermissions.filter((item: Permission) => item.type === '1');
            const buttonPermissions = allPermissions.filter((item: Permission) => item.type === '2');
            
            // 构建导航项：只有对应的按钮权限存在时才显示导航项
            // const navItems = modulePermissions.filter((module: Permission) => {
            //   const correspondingButton = buttonPermissions.find((button: Permission) => 
            //     button.name.includes(module.name) && button.name.includes('进入管理后台')
            //   );
            //   return correspondingButton;
            // });
            const navItems = modulePermissions;
            
            set({
              permissions: allPermissions,
              modulePermissions,
              buttonPermissions,
              navItems,
            });
            
          }
        } catch (error) {
          console.error('获取权限失败:', error);
        } finally {
          setLoading(false);
        }
      },

      // 获取用户角色
      fetchUserRoles: async () => {
        try {
          const response = await getUserRolesApi();
          if (response.code === 200) {
            set({ userRoles: response.data });
            localStorage.setItem('userRoles', JSON.stringify(response.data));
          }
        } catch (error) {
          console.error('获取用户角色失败:', error);
        }
      },

      // 设置加载状态
      setLoading: (loading: boolean) => {
        set({ loading });
      },
    }),
    {
      name: 'auth-store',
      partialize: (state) => ({
        isAuthenticated: state.isAuthenticated,
        userInfo: state.userInfo,
        userRoles: state.userRoles,
        permissions: state.permissions,
        modulePermissions: state.modulePermissions,
        buttonPermissions: state.buttonPermissions,
        navItems: state.navItems,
      }),
    }
  )
);
