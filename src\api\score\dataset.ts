import request from "../../services/request"

interface UploadDatasetParams {
  importDataset: any
}

interface ProjectQueryParams {
  current: number
  size: number
  isScoreBasisUpload?: boolean
  projectName?: string
  projectType?: string
}

interface ProjectQueryData {
  records: any[]
  total: number
  current: number
  size: number
}

interface ProjectQueryResponse {
  data: {
    code: number
    data: ProjectQueryData
    message?: string
  }
}

/**
 * 上传数据集
 * @param params 上传文件
 * @returns
 */

export function uploadDataset(params: UploadDatasetParams) {
  const formData = new FormData()
  formData.append("file", params.importDataset)
  return request.post("/file/uploadInCreateProject", formData)
}
/**
 * 删除数据集
 * @param deleteIds 需要删除的数据集ID
 * @returns
 */

export function deleteDataset(deleteIds: string[]) {
  return request.delete("/dataset", { data: { deleteIds } })
}

/**
 * 查询项目列表
 * @param params 查询参数
 * @returns
 */
export function queryProjectList(params: ProjectQueryParams): Promise<ProjectQueryResponse> {
  return request.post("/leader/project/query", params)
}

/**
 * 删除单个项目
 * @param id 项目ID
 * @returns
 */
export function deleteProject(id: string) {
  return request.delete(`/leader/project/delete/${id}`)
}

/**
 * 批量删除项目
 * @param idList 项目ID列表
 * @returns
 */
export function batchDeleteProject(idList: string[]) {
  // 将数组转换为查询参数格式
  const params = new URLSearchParams()
  idList.forEach(id => params.append("idList", id))

  return request.delete(`/leader/project/batchDelete?${params.toString()}`)
}
