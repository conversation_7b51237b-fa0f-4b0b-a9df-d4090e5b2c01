.profile-card {
  @apply rounded-lg border border-gray-200 bg-white p-8 shadow-sm;
}

.profile-form {
  @apply space-y-6;
}

.form-row {
  @apply flex items-center border-b border-gray-100 py-4 last:border-b-0;
}

.form-label {
  @apply w-24 flex-shrink-0 text-sm font-medium text-black;
}

.form-value {
  @apply flex items-center justify-between;
}

.value-text {
  @apply text-sm text-black;
}

.edit-icon {
  @apply ml-2 cursor-pointer text-xs text-blue-500 hover:text-blue-600;
}

.edit-input {
  @apply border-gray-300 focus:border-blue-500 focus:ring-blue-500;
}

.role-tag {
  @apply inline-block rounded-full bg-blue-100 px-3 py-1 text-xs font-medium text-blue-800;
}

.cancel-btn {
  @apply border-gray-300 text-gray-600 hover:border-gray-400 hover:text-gray-700;
}

.save-btn {
  @apply border-blue-500 bg-blue-500 hover:border-blue-600 hover:bg-blue-600;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .profile-container {
    @apply p-4;
  }

  .profile-card {
    @apply p-6;
  }

  .form-row {
    @apply flex-col items-start space-y-2;
  }

  .form-label {
    @apply w-full;
  }

  .form-value {
    @apply w-full;
  }
}
