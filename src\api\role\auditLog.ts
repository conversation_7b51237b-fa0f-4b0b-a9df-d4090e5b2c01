import request from '../../services/request';
import {
  ApiResponse,
  OperationLog,
  QueryOperationLogParams,
  OperationLogListResponse
} from '../../types';

// 查询操作日志
export const queryOperationLogApi = (data: QueryOperationLogParams): Promise<ApiResponse<OperationLogListResponse>> => {
  // 过滤掉空值参数
  const filteredData = Object.fromEntries(
    Object.entries(data).filter(([_, value]) =>
      value !== undefined && value !== null && value !== ''
    )
  );

  return request.post('/operationLog/query', filteredData).then(res => res.data);
};

// 导出日志
export const exportOperationLogApi = (): Promise<Blob> => {
  return request.get('/operationLog/export', {
    responseType: 'blob',headers: {
      Accept: 'application/octet-stream'
    }
  }).then(res => res.data);
};

// 清空日志
export const truncateAllOperationLogApi = (): Promise<ApiResponse<null>> => {
  return request.delete('/operationLog/truncateAll').then(res => res.data);
};

// 获取操作类型列表
export const getOperationTypesApi = (): Promise<ApiResponse<string[]>> => {
  return request.get('/operationLog/types').then(res => res.data);
};

