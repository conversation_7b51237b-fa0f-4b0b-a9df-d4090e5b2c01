import React, { useRef } from "react"

import { useEffect } from "react"
import * as echarts from "echarts"

const BarChart = () => {
  const chartRef = useRef<any>(null)
  useEffect(() => {
    const chart = echarts.init(chartRef.current)

    // 定义不同状态的颜色配置
    const statusColors = {
      已完成: "#76BD49",
      草稿: "#3E63D2",
      进行中: "#EBB148",
      已取消: "#EB5C56",
      评估中: "#40403F",
      异常: "#D81E06",
    }

    // 数据配置，包含状态信息
    const data = [
      { name: "已完成", value: 150, status: "已完成" },
      { name: "草稿", value: 230, status: "草稿" },
      { name: "进行中", value: 224, status: "进行中" },
      { name: "已取消", value: 218, status: "已取消" },
      { name: "评估中", value: 135, status: "评估中" },
      { name: "异常", value: 147, status: "异常" },
    ]

    chart.setOption({
      // 提示框配置
      tooltip: {
        trigger: "axis",
        axisPointer: {
          type: "none", // 隐藏坐标轴指示器
        },
        backgroundColor: "#D6E9ED",
        borderColor: "rgba(0, 0, 0, 0.20)",
        borderWidth: 2,
        borderRadius: 6,
        textStyle: {
          color: "#333",
        },
        extraCssText:
          "border-radius: 6px; border: 2px solid rgba(0, 0, 0, 0.20); background: #D6E9ED; backdrop-filter: blur(16.45315170288086px);",
      },
      grid: {
        left: "10%",
        right: "10%",
        top: "10%",
        bottom: "10%",
        containLabel: true,
      },
      xAxis: {
        type: "category",
        data: data.map(item => item.name),
        axisLabel: {
          show: true,
          margin: 20, // 增加标签与轴的距离
          textStyle: {
            fontSize: 12,
          },
        },
        axisLine: {
          show: false, // 隐藏x轴线
        },
        axisTick: {
          show: false, // 隐藏x轴刻度线
        },
      },
      yAxis: {
        type: "value",
        axisLabel: {
          show: false, // 隐藏y轴刻度标签
        },
        axisLine: {
          show: false, // 隐藏y轴线
        },
        axisTick: {
          show: false, // 隐藏y轴刻度线
        },
        splitLine: {
          show: false, // 隐藏网格线
        },
      },
      series: [
        {
          data: data.map(item => ({
            value: item.value,
            itemStyle: {
              color: statusColors[item.status],
              borderRadius: [4, 4, 4, 4],
              borderColor: "#333",
              borderWidth: 1,
              opacity: ["已完成", "进行中", "已取消"].includes(item.status) ? 0.66 : 1,
            },
          })),
          type: "bar",
          barWidth: "60%",
          label: {
            show: true,
            position: "insideBottom",
            color: "#fff",
            fontSize: 12,
            fontWeight: "bold",
          },
          showBackground: true,
          backgroundStyle: {
            color: "#D6E9ED",
            borderColor: "#ccc",
            borderWidth: 1,
          },
        },
      ],
    })

    const handleResize = () => {
      chart.resize()
    }

    window.addEventListener("resize", handleResize)

    return () => {
      window.removeEventListener("resize", handleResize)
      chart.dispose()
    }
  }, [])
  return (
    <div className="relative z-[10] h-full w-full">
      <div ref={chartRef} style={{ height: "100%", width: "100%" }} />
    </div>
  )
}

export default BarChart
