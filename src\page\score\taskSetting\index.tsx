import React, { useState } from "react"
import { Button, Collapse, Form, Input, Select, message, Space } from "antd"
import { EditOutlined, SaveOutlined, DeleteOutlined } from "@ant-design/icons"
import type { CollapseProps } from "antd"
import goback from "@/assets/goback.svg"
import { useLocation, useNavigate } from "react-router-dom"
import "../index.css"
import { numberToChinese } from "../types"

interface TaskData {
  id: string
  name: string
  participants: string[]
  description: string
  isEditing: boolean
  isSaved: boolean
}

const TaskSetting = () => {
  const location = useLocation()
  const navigate = useNavigate()
  const taskId = location.state?.key
  const [form] = Form.useForm()

  // 初始化任务数据
  const [tasks, setTasks] = useState<TaskData[]>([
    {
      id: "1",
      name: "",
      participants: [],
      description: "",
      isEditing: true,
      isSaved: false,
    },
  ])
  const goToScore = () => {
    navigate("/index/score/project/" + taskId)
  }

  // 模拟参与人选项
  const participantOptions = [
    { label: "张三", value: "zhangsan" },
    { label: "李四", value: "lisi" },
    { label: "王五", value: "wangwu" },
    { label: "赵六", value: "zhaoliu" },
  ]

  // 验证任务名称（不允许特殊字符，最多15个字符）
  const validateTaskName = (value: string) => {
    if (!value) return false
    if (value.length > 15) return false
    const specialCharRegex = /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/
    return !specialCharRegex.test(value)
  }

  // 检查必填项是否已填写
  const checkRequiredFields = (task: TaskData) => {
    return task.name && task.participants.length > 0 && validateTaskName(task.name)
  }

  // 编辑任务
  const handleEdit = (taskId: string) => {
    setTasks(prev => prev.map(task => (task.id === taskId ? { ...task, isEditing: true } : task)))
  }

  // 保存任务
  const handleSave = (taskId: string) => {
    const task = tasks.find(t => t.id === taskId)
    if (!task) return

    if (!checkRequiredFields(task)) {
      message.error("请填写必填项")
      return
    }

    setTasks(prev =>
      prev.map(task => (task.id === taskId ? { ...task, isEditing: false, isSaved: true } : task))
    )
    message.success("任务保存成功")
  }

  // 删除任务
  const handleDelete = (taskId: string) => {
    if (tasks.length === 1) {
      message.warning("至少需要保留一个任务")
      return
    }
    setTasks(prev => prev.filter(task => task.id !== taskId))
    message.success("任务删除成功")
  }

  // 更新任务数据
  const updateTask = (taskId: string, field: keyof TaskData, value: any) => {
    setTasks(prev =>
      prev.map(task => (task.id === taskId ? { ...task, [field]: value, isSaved: false } : task))
    )
  }

  // 新增任务
  const handleAddTask = () => {
    // 检查是否有未保存的任务
    const hasUnsavedTask = tasks.some(task => task.isEditing && !task.isSaved)
    if (hasUnsavedTask) {
      const unsavedTask = tasks.find(task => task.isEditing && !task.isSaved)
      if (unsavedTask && !checkRequiredFields(unsavedTask)) {
        message.error("请填写必填项")
        return
      }
      message.error("请先保存当前任务")
      return
    }

    const newTaskId = (tasks.length + 1).toString()
    const newTask: TaskData = {
      id: newTaskId,
      name: ``,
      participants: [],
      description: "",
      isEditing: true,
      isSaved: false,
    }
    setTasks(prev => [...prev, newTask])
  }

  // 确认按钮
  const handleConfirm = () => {
    // 检查是否有未保存的任务
    const hasUnsavedTask = tasks.some(task => task.isEditing && !task.isSaved)
    if (hasUnsavedTask) {
      const unsavedTask = tasks.find(task => task.isEditing && !task.isSaved)
      if (unsavedTask && !checkRequiredFields(unsavedTask)) {
        message.error("请填写必填项")
        return
      }
      message.error("请先保存当前任务")
      return
    }

    // 打印所有任务数据
    const filteredTasks = tasks.map(task => {
      // 解构出需要排除的属性，剩下的属性放入 rest
      const { isEditing, isSaved, ...rest } = task
      return rest
    })

    // 打印处理后的数据
    console.log("所有任务数据:", filteredTasks)
    message.success("任务设置完成")
  }

  // 生成折叠面板项
  const generateCollapseItems = (): CollapseProps["items"] => {
    return tasks.map(task => ({
      key: task.id,
      label: (
        <div className="flex w-full items-center justify-between">
          <div className="flex gap-4 text-base font-bold">
            <span>{`任务${numberToChinese(task.id)}`}</span>
            {task.isSaved && <span>{task.name}</span>}
          </div>

          <Space onClick={e => e.stopPropagation()}>
            {task.isEditing ? (
              <>
                <Button onClick={() => handleSave(task.id)}>保存</Button>
                <Button onClick={() => handleDelete(task.id)}>删除</Button>
              </>
            ) : (
              <>
                <Button onClick={() => handleEdit(task.id)}>编辑</Button>
                <Button onClick={() => handleDelete(task.id)}>删除</Button>
              </>
            )}
          </Space>
        </div>
      ),
      children: (
        <div className="space-y-4">
          <Form form={form} labelCol={{ span: 3 }} wrapperCol={{ span: 18 }}>
            <Form.Item
              label="任务名称"
              required
              validateStatus={task.name && !validateTaskName(task.name) ? "error" : ""}
              help={
                task.name && !validateTaskName(task.name)
                  ? "任务名称不能超过15个字符且不能包含特殊字符"
                  : ""
              }
            >
              <Input
                value={task.name}
                onChange={e => updateTask(task.id, "name", e.target.value)}
                placeholder="请输入任务名称"
                maxLength={15}
                disabled={!task.isEditing}
              />
            </Form.Item>

            <Form.Item label="任务参与人" required>
              <Select
                mode="multiple"
                value={task.participants}
                onChange={value => updateTask(task.id, "participants", value)}
                options={participantOptions}
                placeholder="请选择任务参与人"
                disabled={!task.isEditing}
              />
            </Form.Item>

            <Form.Item label="任务描述">
              <Input.TextArea
                value={task.description}
                onChange={e => updateTask(task.id, "description", e.target.value)}
                placeholder="请输入任务描述（可选）"
                rows={3}
                disabled={!task.isEditing}
              />
            </Form.Item>
          </Form>
        </div>
      ),
    }))
  }

  return (
    <div className="stting-content flex h-full min-h-screen flex-col bg-gradient-to-b from-[#E1F3FB] via-[#F3F7FB] to-[#F8FAFC] p-8">
      <div className="sticky top-0 z-20 flex h-16 items-center justify-between bg-[#E1F3FB]">
        <div className="flex items-center gap-3">
          <img
            src={goback}
            className="h-9 w-9 cursor-pointer"
            onClick={() => goToScore()}
            alt="goback"
          />
          <span className="text-xl text-[#111]">任务设置</span>
        </div>
        <Space>
          <Button type="primary" onClick={handleAddTask}>
            新建任务
          </Button>
        </Space>
      </div>

      <div className="flex-1 p-6">
        <Collapse
          items={generateCollapseItems()}
          size="large"
          activeKey={tasks.map(task => task.id)}
        />
      </div>

      <div className="flex justify-end border-t p-6">
        <Button className="!bg-[#111] !text-[#fff]" onClick={handleConfirm}>
          确认
        </Button>
      </div>
    </div>
  )
}

export default TaskSetting
