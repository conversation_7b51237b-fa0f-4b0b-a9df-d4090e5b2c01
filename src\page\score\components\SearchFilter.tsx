import React from "react"
import { Input, Select, Space } from "antd"
import { SearchOutlined } from "@ant-design/icons"
import {
  SearchFilterProps,
  PM_OPTIONS,
  SORT_OPTIONS,
  MODEL_EVALUATION_OPTIONS,
  SYSTEM_OPTIONS,
  SCORE_OPTIONS,
} from "../types"
const SearchFilter: React.FC<SearchFilterProps> = ({
  filterAttribute,
  sortDirection,
  filterInput,
  onFilterAttributeChange,
  onSortDirectionChange,
  onFilterInputChange,
  onSearch,
  type,
}) => {
  const handleKeyPress = (event: React.KeyboardEvent<HTMLInputElement>) => {
    if (event.key === "Enter") {
      onSearch()
    }
  }
  const typeOptionsMap = new Map([
    ["pm", PM_OPTIONS],
    ["model", MODEL_EVALUATION_OPTIONS],
    ["system", SYSTEM_OPTIONS],
    ["score", SCORE_OPTIONS],
  ])
  const currentOptions = typeOptionsMap.get(type) || []

  // 获取当前选中的选项的placeholder
  const currentPlaceholder =
    currentOptions.find(option => option.value === filterAttribute)?.placeholder || "请输入搜索内容"

  return (
    <Space size={10}>
      <Space.Compact>
        <Select
          size="large"
          className="filter-select"
          value={filterAttribute}
          onChange={onFilterAttributeChange}
          options={currentOptions}
        />
        <Input
          size="large"
          className="filter-input"
          suffix={<SearchOutlined />}
          placeholder={currentPlaceholder}
          value={filterInput}
          onChange={e => onFilterInputChange && onFilterInputChange(e.target.value)}
          onKeyUp={handleKeyPress}
          allowClear
        />
      </Space.Compact>
      {type !== "system" && type !== "score" && (
        <Select
          size="large"
          className="filter-select"
          value={sortDirection}
          placeholder="请选择评分状态"
          onChange={onSortDirectionChange}
          style={{ width: "11.75rem" }}
          options={SORT_OPTIONS}
          allowClear
        />
      )}
    </Space>
  )
}

export default SearchFilter
