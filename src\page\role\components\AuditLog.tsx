"use client"
import React, { useState, useEffect, memo } from "react"
import {
  Table,
  Input,
  Pagination,
  Modal,
  Button,
  Select,
  DatePicker,
  DatePickerProps,
  message,
  ConfigProvider,
} from "antd"
import { DeleteOutlined, DownloadOutlined, EyeOutlined } from "@ant-design/icons"
import dayjs from "dayjs"
import { OperationLog, QueryOperationLogParams } from "../../../types"
import locale from "antd/locale/zh_CN"

import "dayjs/locale/zh-cn"

dayjs.locale("zh-cn")

interface AuditLogProps {
  logs: OperationLog[]
  loading: boolean
  pagination: {
    currentPage: number
    total: number
    pageSize: number
  }
  setPagination: (pagination: any) => void
  search: string
  setSearch: (search: string) => void
  filters: {
    module: string
    typeId: string
    userName: string
    startTime: string
    endTime: string
  }
  setFilters: (filters: any) => void
  operationTypes: { value: number; label: string }[]
  getLogList: () => void
  exportLog: () => Promise<Blob>
  truncateAllLogs: () => Promise<any>
}

const AuditLog: React.FC<AuditLogProps> = ({
  logs,
  loading,
  pagination,
  setPagination,
  search,
  setSearch,
  filters,
  setFilters,
  operationTypes,
  getLogList,
  exportLog,
  truncateAllLogs,
}) => {
  const [modalVisible, setModalVisible] = useState(false)
  const [currentLog, setCurrentLog] = useState<OperationLog | null>(null)
  const [exportLoading, setExportLoading] = useState(false)
  const [clearLoading, setClearLoading] = useState(false)
  const [searchValue, setSearchValue] = useState(search)

  // 处理分页变化
  const handlePaginationChange = (page: number, pageSize?: number) => {
    setPagination({
      ...pagination,
      currentPage: page,
      pageSize: pageSize || pagination.pageSize, // 更新 pageSize
    })
    // getLogList() // 记得重新拉取数据
  }

  // 处理筛选条件变化
  const handleFilterChange = () => {
    // 重置到第一页
    setPagination({
      ...pagination,
      currentPage: 1,
    })
    // 触发数据刷新
    getLogList()
  }

  // 导出日志
  const handleExport = async () => {
    try {
      setExportLoading(true)
      const blob = await exportLog()
      const url = window.URL.createObjectURL(blob)
      const a = document.createElement("a")
      a.href = url
      a.download = `操作日志_${dayjs().format("YYYY-MM-DD")}.xlsx`
      document.body.appendChild(a)
      a.click()
      window.URL.revokeObjectURL(url)
      document.body.removeChild(a)
      message.success("导出成功")
    } catch (error) {
      console.error("导出失败:", error)
      message.error("导出失败")
    } finally {
      setExportLoading(false)
    }
  }

  // 清空日志
  const handleClearAll = () => {
    Modal.confirm({
      title: "确认清空所有日志？",
      content: "此操作不可恢复，请谨慎操作",
      onOk: async () => {
        try {
          setClearLoading(true)
          const response = await truncateAllLogs()
          if (response.code === 200) {
            message.success("清空成功")
            getLogList()
          }
        } catch (error) {
          console.error("清空失败:", error)
          message.error("清空失败")
        } finally {
          setClearLoading(false)
        }
      },
    })
  }

  const columns = [
    {
      title: "用户名",
      dataIndex: "uName",
      key: "uName",
    },
    {
      title: "操作模块",
      dataIndex: "mod",
      key: "mod",
    },
    {
      title: "操作类型",
      dataIndex: "ty",
      key: "ty",
    },
    {
      title: "IP地址",
      dataIndex: "addr",
      key: "addr",
    },
    {
      title: "操作时间",
      dataIndex: "createTime",
      key: "createTime",
    },
    {
      title: "状态",
      dataIndex: "isException",
      key: "isException",
      render: (isException: boolean) => (
        <span className={isException ? "text-red-500" : "text-green-500"}>
          {isException ? "失败" : "成功"}
        </span>
      ),
    },
    {
      title: "操作",
      key: "action",
      render: (_: any, record: OperationLog) => (
        <EyeOutlined
          className="cursor-pointer text-blue-500"
          onClick={() => {
            setCurrentLog(record)
            setModalVisible(true)
          }}
        />
      ),
    },
  ]

  useEffect(() => {
    const handler = setTimeout(() => {
      setSearch(searchValue) // 更新父状态
      setPagination({ ...pagination, currentPage: 1 }) // 搜索时回到第一页
      // getLogList()
    }, 500) // 500ms 防抖

    return () => clearTimeout(handler)
  }, [searchValue])

  // 时间选择处理
  const handleStartTimeChange: DatePickerProps["onChange"] = (date, dateString) => {
    setFilters(prev => ({ ...prev, startTime: dateString as string }))
  }

  const handleEndTimeChange: DatePickerProps["onChange"] = (date, dateString) => {
    setFilters(prev => ({ ...prev, endTime: dateString as string }))
  }

  return (
    <div className="border-border card-shadow mb-8 rounded-xl border bg-white p-6 shadow-sm">
      <div className="mb-6 flex items-center justify-between">
        <h3 className="text-xl font-bold text-gray-800">审计日志</h3>
        <div className="flex space-x-2">
          <Button
            className="rounded bg-gray-100 px-3 py-1 text-sm text-gray-700 hover:bg-gray-200"
            onClick={() => handleExport()}
            icon={<DownloadOutlined />}
            loading={exportLoading}
            type="primary"
          >
            导出
          </Button>
          <Button
            className="rounded bg-red-100 px-3 py-1 text-sm text-red-700 hover:bg-red-200"
            onClick={() => handleClearAll()}
            danger
            type="primary"
            icon={<DeleteOutlined />}
          >
            清空
          </Button>
        </div>
      </div>
      <div className="mb-4 flex justify-between gap-4">
        <div className="w-1/4">
          <label className="mb-2 block text-sm font-medium text-gray-700">操作类型</label>
          <Select
            placeholder="选择操作类型"
            className="w-full"
            onChange={v => setFilters(prev => ({ ...prev, typeId: v as string }))}
            options={operationTypes}
            allowClear
          />
        </div>
        <div className="w-1/4">
          <label className="mb-2 block text-sm font-medium text-gray-700">用户</label>
          <Input
            className="w-full"
            placeholder="搜索用户"
            allowClear
            onChange={e => setSearchValue(e.target.value)}
          />
        </div>
        <div className="w-1/4">
          <label className="mb-2 block text-sm font-medium text-gray-700">开始日期</label>
          <ConfigProvider locale={locale}>
            <DatePicker
              className="w-full"
              onChange={handleStartTimeChange}
              showTime
              format="YYYY-MM-DD HH:mm:ss"
              allowClear
            />
          </ConfigProvider>
        </div>
        <div className="w-1/4">
          <label className="mb-2 block text-sm font-medium text-gray-700">结束日期</label>
          <ConfigProvider locale={locale}>
            <DatePicker
              className="w-full"
              onChange={handleEndTimeChange}
              showTime
              format="YYYY-MM-DD HH:mm:ss"
              allowClear
            />
          </ConfigProvider>
        </div>
      </div>
      {/* <div className="mb-4">
        <Button
          type="primary"
          onClick={handleFilterChange}
        >
          查询
        </Button>
      </div> */}
      <Table rowKey="id" dataSource={logs} columns={columns} loading={loading} pagination={false} />

      <div className="flex items-center justify-end border-t border-neutral-200 bg-white px-4 py-3">
        <Pagination
          current={pagination.currentPage}
          total={pagination.total}
          pageSize={pagination.pageSize}
          showSizeChanger
          showTotal={total => `共 ${total} 条记录`}
          onChange={handlePaginationChange}
        />
      </div>

      <Modal
        title="日志详情"
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        footer={null}
        width={600}
      >
        {currentLog && (
          <div className="space-y-3">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <span className="font-medium text-gray-600">用户名：</span>
                <span>{currentLog.uName}</span>
              </div>
              <div>
                <span className="font-medium text-gray-600">操作模块：</span>
                <span>{currentLog.mod}</span>
              </div>
              <div>
                <span className="font-medium text-gray-600">操作类型：</span>
                <span>{currentLog.ty}</span>
              </div>
              <div>
                <span className="font-medium text-gray-600">IP地址：</span>
                <span>{currentLog.addr}</span>
              </div>
              <div>
                <span className="font-medium text-gray-600">操作时间：</span>
                <span>{currentLog.createTime}</span>
              </div>
              <div>
                <span className="font-medium text-gray-600">状态：</span>
                <span className={currentLog.isException ? "text-red-500" : "text-green-500"}>
                  {currentLog.isException ? "失败" : "成功"}
                </span>
              </div>
            </div>
            {currentLog.description && (
              <div>
                <span className="font-medium text-gray-600">操作描述：</span>
                <p className="mt-1 text-gray-800">{currentLog.description}</p>
              </div>
            )}
            {currentLog.params && (
              <div>
                <span className="font-medium text-gray-600">请求参数：</span>
                <pre className="mt-1 max-h-32 overflow-auto rounded bg-gray-100 p-2 text-sm">
                  {currentLog.params}
                </pre>
              </div>
            )}
            {currentLog.res && (
              <div>
                <span className="font-medium text-gray-600">返回结果：</span>
                <pre className="mt-1 max-h-32 overflow-auto rounded bg-gray-100 p-2 text-sm">
                  {currentLog.res}
                </pre>
              </div>
            )}
            {currentLog.msg && (
              <div>
                <span className="font-medium text-gray-600">异常信息：</span>
                <p className="mt-1 text-red-600">{currentLog.msg}</p>
              </div>
            )}
          </div>
        )}
      </Modal>
    </div>
  )
}

export default memo(AuditLog)
