.preview-file-modal {
  display: flex;
  flex-direction: row;
  gap: 32px;
  width: 100%;
  align-items: flex-start;
  padding-top: 24px;
}

.preview-file-main {
  flex: 1;
  font-size: 16px;
  /* max-height: 100%; */
}

.preview-tree {
  width: 180px;
  height: 100%;
}

.preview-switch-btn {
  width: 88px;
  height: 80px;
  flex-shrink: 0;
  border-radius: 8px;
  border: 1px solid #111;
  background: #fff;
}

.preview-switch-btn-item {
  height: 40px;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  cursor: pointer;
}

.preview-switch-btn-item.active {
  background: #111;
  color: white;
}

.modal-title {
  display: flex;
  gap: 12px;
  align-items: center;
}

.back-btn {
  font-size: 14px;
  width: 88px;
  height: 36px;
  gap: 2px;
  color: #7886aa;
}
.preview-tree {
  display: flex;
  flex-direction: column;
}
.no-data {
  color: rgb(142, 152, 167);
  margin-top: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  margin-top: 26%;
  gap: 8px;
  color: #8e98a7;
}
.tree-header {
  color: #8e98a7;
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.preview-bt {
  width: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-right: 1px solid #f0f0f0;
}
