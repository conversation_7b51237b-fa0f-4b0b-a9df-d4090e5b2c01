version: '3.8'

services:
  # 前端应用
  frontend:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "3000:80"
    volumes:
      - ./nginx.conf:/etc/nginx/conf.d/default.conf
    depends_on:
      - nginx
    networks:
      - app-network

  # Nginx代理服务
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
    volumes:
      - ./nginx.conf:/etc/nginx/conf.d/default.conf
      - ./dist:/usr/share/nginx/html
    networks:
      - app-network
    restart: unless-stopped

networks:
  app-network:
    driver: bridge

# 使用说明:
# 1. 构建并启动服务: docker-compose up -d
# 2. 停止服务: docker-compose down
# 3. 查看日志: docker-compose logs -f
# 4. 重新构建: docker-compose up --build