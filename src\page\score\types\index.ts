// 分页参数类型
export interface PaginationState {
  page: number
  size: number
  total: number
}

// 搜索筛选参数类型
export interface SearchFilterState {
  filterAttribute: string
  sortAttribute?: string
  sortDirection?: boolean
  filterTaskName?: string
  filterInput?: string
}

// 任务选择状态类型
export interface TaskSelectionState {
  selectedRows: IndicatorItem[]
  selectedRowKeys: Set<string>
}

// 模态框状态类型
export interface ModalState {
  uploadErrorModal: boolean
  exportModal: boolean
  reviewConfigModal: boolean
}

// 任务操作类型
export interface TaskOperations {
  onDelete: (taskIds: string[]) => void
  onExport: (tasks: IndicatorItem[]) => void
  onRename: (taskId: string, newName: string) => void
  onReviewConfig: (taskId: string) => void
  onApply: (e: React.MouseEvent<HTMLElement>, rowData: IndicatorItem) => void
}

// 搜索筛选组件Props
export interface SearchFilterProps {
  filterAttribute: string
  sortDirection?: boolean
  filterInput?: string
  onFilterAttributeChange?: (value: string) => void
  onSortDirectionChange?: (value: boolean) => void
  onFilterInputChange?: (value: string) => void
  onSearch: () => void
  type: string
}

// 操作按钮组件Props
export interface ActionButtonsProps {
  selectedRows?: (IndicatorItem | ModelEvaluationItem | PerformanceLeaderboardItem)[]
  type: string
  onDelete?: () => void
  onExport?: () => void
  onUpload?: () => void
  onCreate?: () => void
}

// 任务表格组件Props
export interface TaskTableProps {
  evalutionData: IndicatorItem[]
  apiFlg: boolean
  selectedRowKeys: Set<string>
  renameList?: Set<string>
  tableIndex: number
  onSelectionChange?: (selectedRowKeys: React.Key[], selectedRows: IndicatorItem[]) => void
  onRename?: (taskId: string) => void
  onDelete?: (taskId: string) => void
  onApply?: (e: React.MouseEvent<HTMLElement>, rowData: IndicatorItem) => void
  onProblemDetail?: (taskId: string) => void
  onRetry?: (taskId: string) => void
  onDetail?: (taskId: string, rowData?: IndicatorItem) => void
  onVisibleRangeChange?: (taskId: string, users: string[]) => void
  getTasksList?: () => void
  setRenameList?: React.Dispatch<React.SetStateAction<Set<string>>>
  openIndicatorMoadl?: (rowData: IndicatorItem) => void
  hiddenColumns?: string[] // 自定义隐藏列（dataIndex），用于复用表格时按需隐藏
}

// 输入组件Props
export interface InputComponentProps {
  taskInfo: IndicatorItem
  renameList: Set<string>
  setRenameList: React.Dispatch<React.SetStateAction<Set<string>>>
  getTasksList: () => void
}

// 主组件Props
export interface TaskViewProps {
  // 如果需要从父组件传递props，在这里定义
}

// 任务状态枚举
export enum TaskStatusEnum {
  SUCCESS = "SUCCESS",
  PROCESSING = "PROCESSING",
  FAILED = "FAILED",
  ERROR = "ERROR",
}

// 菜单点击事件类型
export interface MenuClickEvent {
  key: string
  taskId: string
}

// API调用状态类型
export interface ApiState {
  loading: boolean
  error?: string
}
export function numberToChinese(num) {
  if (num <= 10) {
    const chineseNumbers = ["零", "一", "二", "三", "四", "五", "六", "七", "八", "九", "十"]
    return chineseNumbers[num]
  } else if (num < 20) {
    return "十" + numberToChinese(num - 10)
  } else if (num < 100) {
    const tens = Math.floor(num / 10)
    const ones = num % 10
    return (tens > 1 ? numberToChinese(tens) : "") + "十" + (ones > 0 ? numberToChinese(ones) : "")
  }
  // 可以继续扩展处理更大的数字
  return num.toString()
}

// 项目管理筛选选项
export const PM_OPTIONS = [
  { value: "projectName", label: "项目名称", placeholder: "请输入项目名称" },
  { value: "projectType", label: "项目类型", placeholder: "请输入项目类型" },
  // { value: "projectLeader", label: "负责人", placeholder: "请输入负责人" },
  // { value: "projectStatus", label: "评分状态", placeholder: "请输入评分状态" },
]
// 排序选项
export const SORT_OPTIONS = [
  { value: true, label: "评分依据已上传" },
  { value: false, label: "评分依据未上传" },
]

//指标管理列表
export interface PMItem {
  id: string
  projectName: string
  projectType: string
  projectLeader: string
  participants: string // 参与人
  evaluator?: string // 评分人
  createdTime: string
  startTime?: string // 开始时间
  endTime?: string // 结束时间
  scoreStatus: string
  materialStatus: string // 材料状态
  scoringBasisUploaded?: boolean // 评分依据是否上传
  projectDescription?: string // 项目描述
  isPublished?: boolean // 是否发布
  score?: number // 分数
}

// 用户角色类型
export interface UserRole {
  account: string
  userName: string
  code: string
  roleName: string
}

// 角色字符串类型（用于表格列配置）
export type UserRoleType = "project_manager" | "evaluator" | "user"

// 角色代码枚举
export enum RoleCode {
  ADMIN = "ADMIN", // 系统管理员
  PROJECT_MANAGER = "PROJECT_MANAGER", // 项目负责人
  EVALUATOR = "EVALUATOR", // 评分人
  USER = "USER", // 普通用户
}

// 项目操作类型
export interface ProjectOperations {
  onUploadScoringBasis?: (projectId: string, record?: PMItem) => void // 上传评分依据
  onPublish?: (projectId: string, record?: PMItem) => void // 发布
  onEdit?: (projectId: string, record?: PMItem) => void // 编辑
  onDelete?: (projectId: string, record?: PMItem) => void // 删除
  onManualScore?: (projectId: string, record?: PMItem) => void // 人工评分
  onUploadMaterial?: (projectId: string, record?: PMItem) => void // 上传材料
  onTrace?: (projectId: string, record?: PMItem) => void // 追溯
  onAnalyze?: (projectId: string, record?: PMItem) => void // 项目分析
}

// 指标项目类型
export interface IndicatorItem {
  id: string | number
  taskName?: string
  taskId?: string
  creator?: string
  createTime?: string
  updateTime?: string
  status?: string
  progress?: number
  score?: number
  description?: string
  type?: string
  [key: string]: any // 允许其他动态属性
}

// 体系管理
export const SYSTEM_OPTIONS = [
  { value: "frameworkName", label: "体系", placeholder: "请输入体系名称" },
  { value: "sceneName", label: "场景", placeholder: "请输入场景名称" },
]

// 模型评估筛选
export const MODEL_EVALUATION_OPTIONS = [
  { value: "taskName", label: "任务名称", placeholder: "请输入任务名称" },
  { value: "modelName", label: "模型名称", placeholder: "请输入模型名称" },
  { value: "frameworkName", label: "评估体系", placeholder: "请输入评估体系" },
  { value: "sceneName", label: "适用领域", placeholder: "请输入适用领域" },
]

// 得分筛选
export const SCORE_OPTIONS = [{ value: "keyWork", label: "关键词", placeholder: "请输入关键词" }]

export interface ModelEvaluationItem {
  id: number
  creator: string // 创建人
  createTime: string
  frameworkId?: number
  frameworkName?: string
  sceneId?: number
  sceneName?: string
  modelId?: number
  modelName?: string
  progress?: number
  qaTaskIds?: string[]
  score?: number
  taskName?: string
  updateTime?: string
}

export interface ScoreItem {
  id: number
  creator: string // 创建人
  createTime: string
}

export interface FrameworkType {
  id: number
  frameworkName: string
  frameworkDescription: string
  creator: string
  scenes: SceneType[]
}

export interface SceneType {
  id: number
  sceneName: string
  sceneDescription: string
  creator: string
  metrics: MetricType[]
}

export interface MetricType {
  metricId: number
  weight?: number
  description?: string
  score: number
  metricName: string
}

export interface ReportType {
  id: string
  taskName: string
  modelId: number
  modelName: string
  frameworkId: number
  frameworkName: string
  sceneId: number
  sceneName: string
  taskIds: string[]
  taskDataCount: number
  score: number
  creator: string
}

export interface ModelTaskType {
  task: ReportType
  metrics: MetricType[]
  score: number
}

export interface SystemType {
  creator: string
  id: number
  name: string
  description: string
  frameworkDescription: string
  frameworkName: string
  scenes: SceneType[]
  total: number
}

export interface PerformanceLeaderboardParams {
  modelName?: string
  frameworkName?: string
  sceneName?: string
  page?: number
  pageSize?: number
}

export interface PerformanceLeaderboardItem {
  id?: string // 用于表格行选择的唯一标识
  frameworkName: string
  metrics: MetricType[]
  modelName: string
  sceneName: string
  score: number
}

export interface PerformanceLeaderboardResponse {
  code: number
  data: {
    current: number
    data: PerformanceLeaderboardItem[]
    pageSize: number
    total: number
  }
  message: string
}

export interface qATaskRetryRequest {
  taskId: string
  fileIdList: string[]
}
