.home-container {
  min-height: calc(100vh - 64px);
  background: #f5f5f5;
}

.home-header {
  text-align: center;
  margin-bottom: 40px;
  padding: 40px 0;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.page-title {
  color: #2c3e50;
  margin-bottom: 16px;
  font-weight: 600;
}

.page-description {
  color: #5a6c7d;
  font-size: 16px;
  margin: 0;
  max-width: 600px;
  margin: 0 auto;
}

.home-content {
  max-width: 1200px;
  margin: 0 auto;
}

.permission-card {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
  cursor: pointer;
  height: 200px;
}

.permission-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
}

.permission-card .ant-card-body {
  padding: 24px;
  height: 140px;
  display: flex;
  align-items: center;
}

.permission-card .ant-card-actions {
  border-top: 1px solid #f0f0f0;
  background: #fafafa;
}

.permission-card .ant-card-actions li {
  margin: 8px 0;
}
.text-secondary {
  color: #000;
  font-family: "HarmonyOS Sans SC";
  font-size: 30px;
  font-style: normal;
  font-weight: 700;
  line-height: normal;
  letter-spacing: 1.2px;
}
.text-tip {
  color: #4b5563;
  font-family: "HarmonyOS Sans SC";
  font-size: 20px;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
}
.enterBtn {
  display: flex;
  height: 42px;
  justify-content: center;
  align-items: center;
  gap: 10px;
  flex-shrink: 0;
  align-self: stretch;
  border-radius: 10px;
  background: #499ae5;
}

.card-content {
  display: flex;
  align-items: center;
  width: 100%;
  gap: 16px;
}

.card-icon {
  font-size: 32px;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  border-radius: 8px;
  background: rgba(24, 144, 255, 0.1);
}

.card-info {
  flex: 1;
  min-width: 0;
}

.card-title {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
  line-height: 1.4;
}

.card-description {
  margin: 0;
  font-size: 14px;
  color: #5a6c7d;
  line-height: 1.5;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.empty-state {
  text-align: center;
  padding: 80px 0;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.empty-state .ant-typography {
  color: #8c8c8c;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .home-header {
    padding: 24px 16px;
    margin-bottom: 24px;
  }

  .page-title {
    font-size: 20px;
  }

  .page-description {
    font-size: 14px;
  }

  .permission-card {
    height: auto;
  }

  .permission-card .ant-card-body {
    height: auto;
    min-height: 120px;
  }

  .card-content {
    flex-direction: column;
    text-align: center;
    gap: 12px;
  }

  .card-icon {
    align-self: center;
  }
}

@media (max-width: 576px) {
  .card-content {
    flex-direction: row;
    text-align: left;
  }

  .card-icon {
    font-size: 24px;
    width: 40px;
    height: 40px;
  }
}
