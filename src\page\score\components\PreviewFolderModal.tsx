import { Button, List, message, Modal, Space } from "antd"
import { DataSetType, DatasetFileEnum, DataSetTreeType } from "../utils"
import { useEffect, useState } from "react"
import PreviewFileModal from "./PreviewFileModal"
import folderIcon from "@/assets/img/folder-icon.svg"
import txtIcon from "@/assets/img/txt-icon.svg"
import DocIcon from "@/assets/img/doc-icon.svg"
import PdfIcon from "@/assets/img/pdf-icon.svg"
import ExcelIcon from "@/assets/img/excel-icon.svg"
import JsonIcon from "@/assets/img/json-icon.svg"
import MarkdownIcon from "@/assets/img/md.svg"
import { RollbackOutlined } from "@ant-design/icons"
// import { getDataSetTreeData, deleteFile } from "@/api/dataset"
import Scrollbar from "react-scrollbars-custom"

interface PreviewFolderModalProp {
  // datasetList: DataSetTreeType[];
  dataSet: DataSetType
  visible: boolean
  OnClose: () => void
}

const PreviewFolderModal: React.FC<PreviewFolderModalProp> = ({ dataSet, visible, OnClose }) => {
  const [currentData, setCurrentData] = useState<string>()
  const [displayList, setDisplayList] = useState<DataSetTreeType[]>([])
  const [currentFolder, setCurrentFolder] = useState<DataSetTreeType>()
  const [fileModal, setFileModal] = useState(false)
  // const [fileDatasetName, setFileDatasetName] = useState<string>();
  const [fileData, setFileData] = useState<DataSetTreeType>(new Object() as DataSetTreeType)
  const [historyList, setHistoryList] = useState<DataSetTreeType[][]>([])

  const fileTypeMap = [folderIcon, txtIcon, DocIcon, PdfIcon, ExcelIcon, JsonIcon, MarkdownIcon]

  // useEffect(() => {
  //   if (dataSet && dataSet.id) {
  //     setCurrentData(dataSet.datasetName)
  //     // const list = JSON.parse(JSON.stringify(datasetList)) as DataSetTreeType;
  //     getDataSetTreeData(dataSet.id).then(res => {
  //       if (res.data?.code === 200) {
  //         const list = res.data.data
  //         if (list.children && list.children.length > 0) {
  //           setDisplayList(list.children)
  //         }
  //       }
  //     })
  //   }
  // }, [dataSet])

  const getFileIcon = (fileName: string) => {
    const fileTypeStr = fileName.split(".").pop()
    let fileType = 0
    if (fileTypeStr === "txt") {
      fileType = DatasetFileEnum.Txt
    } else if (fileTypeStr === "doc" || fileTypeStr === "docx") {
      fileType = DatasetFileEnum.Doc
    } else if (fileTypeStr === "pdf") {
      fileType = DatasetFileEnum.Pdf
    } else if (fileTypeStr === "csv" || fileTypeStr === "xlsx") {
      fileType = DatasetFileEnum.Csv
    } else if (fileTypeStr === "json") {
      fileType = DatasetFileEnum.Json - 1
    } else if (fileTypeStr === "md") {
      fileType = DatasetFileEnum.Markdown - 1
    } else if (fileTypeStr === "png" || fileTypeStr === "jpg" || fileTypeStr === "jpeg") {
      fileType = DatasetFileEnum.Json - 1
    }
    return fileTypeMap[fileType]
  }

  const PreviewFileLabel: React.FC<{ data: DataSetTreeType }> = ({ data }) => {
    if (data.type !== "FILE") {
      return (
        <div
          className="preview-list-item-label"
          onClick={() => {
            if (data.children && data.children.length > 0) {
              onPathChange(data)
            }
          }}
        >
          <img src={folderIcon} />
          <label>{data.name}</label>
        </div>
      )
    } else {
      return (
        <div
          className="preview-list-item-label"
          onClick={() => {
            setFileData(data)
            setFileModal(true)
            const fileId = sessionStorage.setItem("fileId", data.fileId)
          }}
        >
          <img src={getFileIcon(data.name)} />
          <label>{data.name}</label>
        </div>
      )
    }
  }

  const onPathChange = (data: DataSetTreeType) => {
    setCurrentFolder(data)
    setCurrentData(`${currentData}/${data.name}`)
    if (data.children) {
      const updateHistory = historyList
      updateHistory.push(displayList)
      setHistoryList(updateHistory)
      setDisplayList(data.children)
    }
  }

  const onBack = (delPath: string) => {
    setCurrentData(`${currentData?.split(`/${delPath}`)[0]}`)
    // console.log(currentFolder);
    const updateHistory = historyList
    const updateDisplay = updateHistory.pop()
    if (updateDisplay) {
      setDisplayList(updateDisplay)
      setHistoryList(updateHistory)
    }
  }

  const onOk = () => {
    OnClose()
    setDisplayList([])
  }

  const onCancel = () => {
    OnClose()
    setDisplayList([])
  }

  return (
    <Modal
      centered
      destroyOnClose
      className="preview-folder-modal"
      title="数据集预览"
      keyboard={false}
      maskClosable={false}
      styles={{ body: { height: "628px" } }}
      width={"870px"}
      open={visible}
      onOk={onOk}
      onCancel={onCancel}
      footer={[]}
    >
      <div className="preview-header">
        {historyList.length > 0 ? (
          <Button
            className="preview-back-btn"
            onClick={() => {
              if (currentFolder) onBack(currentFolder.name)
            }}
          >
            <RollbackOutlined />
            上一层
          </Button>
        ) : null}
        <span className="mediumText">{currentData}</span>
      </div>
      <Scrollbar style={{ height: 570 }}>
        <List
          size="large"
          bordered
          dataSource={displayList}
          renderItem={item => (
            <List.Item className="preview-list-item">
              <PreviewFileLabel data={item}></PreviewFileLabel>
              {item.type === "FILE" ? (
                <Space size={4} className="preview-list-item-btn">
                  <Button
                    type="link"
                    size="small"
                    onClick={() => {
                      setFileData(item)
                      setFileModal(true)
                    }}
                  >
                    预览
                  </Button>
                </Space>
              ) : null}
            </List.Item>
          )}
        />
        <PreviewFileModal
          visible={fileModal}
          datasetName={currentData}
          OnClose={() => {
            setFileModal(false)
          }}
          fileData={fileData}
        />
      </Scrollbar>
    </Modal>
  )
}

export default PreviewFolderModal
