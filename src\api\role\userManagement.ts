import { ApiResponse, OptionItem } from '../../types';
import request from '../../services/request';

// 用户信息接口
export interface User {
  createId: string;
  createTime: string;
  unit: string | null;
  department: string | null;
  roleName: string;
  permissionScope: string;
  id: string;
  acc: string;
  nick: string;
  avatar: string | null;
  mail: string;
  number: string;
  enable: boolean;
  desc: string;
  name?: string;
}

// 用户列表查询参数
export interface QueryUserParams {
  current: number;
  size: number;
  unit?: string;
  roleName?: string;
  department?: string;
  pageable: boolean;
  id?: number;
}

// 用户列表响应数据
export interface UserListResponse {
  records: User[];
  total?: number;
  current?: number;
  size?: number;
}

// 新增用户参数
export interface CreateUserParams {
  account: string;
  department: string;
  password: string;
  roleId: number;
  unit: string;
  email: string;
  phone?: string;
  enable?: boolean;
  name: string;
  description?: string;
}

// 编辑用户参数
export interface UpdateUserParams {
  id: string;
  account?: string;
  department?: string;
  password?: string;
  roleId?: number;
  unit?: string;
  nick?: string;
  mail?: string;
  number?: string;
  ena?: boolean;
  desc?: string;
}

// 获取用户列表
export const queryUserApi = (data: QueryUserParams): Promise<ApiResponse<UserListResponse>> => {
  // 过滤掉空值参数
  const filteredData = Object.fromEntries(
    Object.entries(data).filter(([_, value]) =>
      value !== undefined && value !== null && value !== ''
    )
  );

  return request.post('/account/query', filteredData).then(res => res.data);
};

// 新增用户
export const createUserApi = (data: CreateUserParams): Promise<ApiResponse<null>> => {
  return request.post('/account/insert', data).then(res => res.data);
};

// 删除用户
export const deleteUserApi = (id: string): Promise<ApiResponse<null>> => {
  return request.delete(`/account/delete/${id}`).then(res => res.data);
};

// 重置密码
export const resetPasswordApi = (userId: string): Promise<ApiResponse<null>> => {
  return request.put('/account/resetPassword', null, {
    params: { userId }
  }).then(res => res.data);
};

// 编辑用户
export const updateUserApi = (data: UpdateUserParams): Promise<ApiResponse<null>> => {
  return request.put('/account/update', data).then(res => res.data);
};

// 获取所有部门名称列表
export const getDepartmentsApi = (): Promise<ApiResponse<OptionItem[]>> => {
  return request.get('/account/departments').then(res => res.data);
};

// 获取所有单位名称列表
export const getUnitsApi = (): Promise<ApiResponse<OptionItem[]>> => {
  return request.get('/account/units').then(res => res.data);
};

// 修改用户启用状态（禁用、启用）
export const toggleUserStatusApi = (userId: string): Promise<ApiResponse<null>> => {
  return request.put('/account/toggleStatus', null, {
    params: { userId }
  }).then(res => res.data);
};