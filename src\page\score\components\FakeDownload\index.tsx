import { Progress } from 'antd';
import React, { useState, useEffect } from 'react';

// interface DownloadProps {
//   onProgress: (progress: number) => void;
// }

const FakeDownload: React.FC = () => {
  const [progress, setProgress] = useState<number>(0);

  useEffect(() => {
    const simulateDownload = () => {
      const interval = setInterval(() => {
        // 模拟下载进度增加
        setProgress((prevProgress) => {
          const newProgress = prevProgress + Math.random() * 10;
          // 限制进度不超过100%
          return newProgress >= 100 ? 100 : newProgress;
        });
      }, 500);

      return () => {
        clearInterval(interval);
      };
    };

    simulateDownload();

    // // 通知父组件下载进度
    // const progressInterval = setInterval(() => {
    //   onProgress(progress);
    // }, 1000);

    // return () => {
    //   clearInterval(progressInterval);
    // };
  }, [progress]);

  return (
    <Progress style={{ position: 'absolute', left: '0', bottom: '-16px' }} percent={progress} showInfo={false} size={['100%', 2]} />
  );
};

export default FakeDownload;
