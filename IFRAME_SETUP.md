# iframe应用嵌入配置说明

## 概述

本项目支持通过iframe方式嵌入以下5个外部应用：

1. **知识库管理** - `http://123.57.244.236:18083/xchat-fe/manage/kb`
2. **智能问答** - `http://123.57.244.236:18083/xchat-fe/welcome`
3. **智能PPT生成** - `http://xa1.puhuacloud.com:40610/`
4. **数据治理** - `http://123.57.244.236:18005/files`
5. **智能体构建** - `http://123.57.244.236:18004/signin`

## 路由配置

### 应用内路由

- `/index/data` - 知识库管理
- `/index/qanda` - 智能问答
- `/index/ppt` - 智能PPT生成
- `/index/governance` - 数据治理
- `/index/agentconstruction` - 智能体构建

### 独立应用路由

- `/app/kb` - 知识库管理
- `/app/chat` - 智能问答
- `/app/ppt` - 智能PPT生成
- `/app/data` - 数据治理
- `/app/agent` - 智能体构建

## Nginx代理配置

### 代理路径

- `/api/kb/` → 知识库管理服务
- `/api/chat/` → 智能问答服务
- `/api/ppt/` → 智能PPT生成服务
- `/api/data/` → 数据治理服务
- `/api/agent/` → 智能体构建服务

### 配置特性

- ✅ 跨域支持 (CORS)
- ✅ 请求头转发
- ✅ 错误处理
- ✅ OPTIONS请求处理

## 部署方式

### 方式一：Docker Compose（推荐）

```bash
# 构建并启动服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f

# 停止服务
docker-compose down
```

### 方式二：手动部署

1. 构建前端应用
```bash
pnpm build
```

2. 配置Nginx
```bash
# 复制nginx配置
cp nginx.conf /etc/nginx/conf.d/default.conf

# 复制构建产物
cp -r dist/* /usr/share/nginx/html/

# 重启nginx
sudo systemctl restart nginx
```

## 安全考虑

### iframe安全设置

```typescript
<iframe
  src={pageUrl}
  title={pageTitle}
  className="iframe-frame"
  frameBorder="0"
  allowFullScreen
  sandbox="allow-same-origin allow-scripts allow-forms allow-popups allow-top-navigation"
/>
```

### 沙箱权限说明

- `allow-same-origin` - 允许同源请求
- `allow-scripts` - 允许执行脚本
- `allow-forms` - 允许表单提交
- `allow-popups` - 允许弹窗
- `allow-top-navigation` - 允许顶级导航

## 故障排除

### 常见问题

1. **iframe无法加载**
   - 检查目标服务是否可访问
   - 确认目标服务允许iframe嵌入
   - 检查网络连接

2. **跨域问题**
   - 确认nginx代理配置正确
   - 检查CORS头设置
   - 验证目标服务CORS策略

3. **样式显示异常**
   - 检查CSS样式冲突
   - 确认iframe容器尺寸设置
   - 验证响应式设计

### 调试方法

```bash
# 检查nginx配置
nginx -t

# 查看nginx日志
tail -f /var/log/nginx/error.log

# 测试代理转发
curl -I http://localhost/api/kb/
```

## 开发说明

### 添加新应用

1. 在 `IframePage/index.tsx` 中添加应用配置
2. 在 `App.tsx` 中添加路由
3. 在 `nginx.conf` 中添加代理配置
4. 更新本文档

### 组件结构

```
src/components/IframePage/
├── index.tsx          # 主组件
├── index.css          # 样式文件
└── README.md          # 组件说明
```

## 更新日志

- 2024-01-XX: 初始版本，支持5个应用的iframe嵌入
- 2024-01-XX: 添加nginx代理配置
- 2024-01-XX: 添加Docker部署支持