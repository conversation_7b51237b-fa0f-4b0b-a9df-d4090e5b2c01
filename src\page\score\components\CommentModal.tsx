import { Button, Modal } from 'antd';
import warningIcon from '@/assets/img/warningIcon.svg';

interface DeleteModalProps {
  visible: boolean;
  onConfirm: () => void;
  onCancel: () => void;
  title?: string;
  content: string;
}

const CommentModal: React.FC<DeleteModalProps> = ({
  visible,
  onCancel,
  onConfirm,
  title,
  content,
}) => {
  return (
    <div>
      <Modal
        title={
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <img src={warningIcon} alt="警告" className="warning" />
            <span style={{ marginLeft: 8 }}>提示</span>
          </div>
        }
        centered
        open={visible}
        closable={false}
        width={520}
        className="delete-model"
        footer={[
          <Button className="modal-btn" onClick={onCancel}>
            取消
          </Button>,
          <Button className="modal-btn" onClick={onConfirm}>
            确定
          </Button>,
        ]}
        wrapClassName="custom-modal"
      >
        <p>{content}</p>
      </Modal>
    </div>
  );
};

export default CommentModal;
