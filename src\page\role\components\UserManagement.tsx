"use client"
import React, { useState, memo } from "react"
import { Table, Button, Input, Modal, Form, Select, Pagination, Space, Tag, message } from "antd"
import { User, CreateUserParams, UpdateUserParams } from "../../../api/role/userManagement"
import { PlusOutlined } from "@ant-design/icons"

interface UserManagementProps {
  users: User[]
  loading: boolean
  pagination: {
    currentPage: number
    total: number
    pageSize: number
  }
  setPagination: (pagination: any) => void
  roleFilter: number | undefined
  setRoleFilter: (filter: number | undefined) => void
  unitFilter: string | undefined
  setUnitFilter: (filter: string | undefined) => void
  departmentFilter: string | undefined
  setDepartmentFilter: (filter: string | undefined) => void
  roleOptions: { value: number; label: string }[]
  departmentOptions: { value: string; label: string }[]
  unitOptions: { value: string; label: string }[]
  getUserList: () => void
  createUser: (params: CreateUserParams) => Promise<any>
  updateUser: (params: UpdateUserParams) => Promise<any>
  deleteUser: (id: string) => Promise<any>
  resetPassword: (id: string) => Promise<any>
  toggleUserStatus: (id: string) => Promise<any>
}

const UserManagement: React.FC<UserManagementProps> = ({
  users,
  loading,
  pagination,
  setPagination,
  roleFilter,
  setRoleFilter,
  unitFilter,
  setUnitFilter,
  departmentFilter,
  setDepartmentFilter,
  roleOptions,
  departmentOptions,
  unitOptions,
  getUserList,
  createUser,
  updateUser,
  deleteUser,
  resetPassword,
  toggleUserStatus,
}) => {
  const [submitting, setSubmitting] = useState(false)
  const [modalVisible, setModalVisible] = useState(false)
  const [modalType, setModalType] = useState<"add" | "edit" | "view">("add")
  const [currentUser, setCurrentUser] = useState<User | null>(null)
  const [form] = Form.useForm()

  // 处理分页变化
  const handlePaginationChange = (page: number, pageSize?: number) => {
    setPagination({
      currentPage: page,
      pageSize: pageSize || pagination.pageSize,
      total: pagination.total,
    })
  }

  // 处理筛选条件变化
  const handleFilterChange = () => {
    // 重置到第一页
    setPagination({
      ...pagination,
      currentPage: 1,
    })
    // 触发数据刷新
    getUserList()
  }

  const handleAdd = () => {
    setModalType("add")
    setCurrentUser(null)
    form.resetFields()
    setModalVisible(true)
  }

  const handleEdit = (user: User) => {
    setModalType("edit")
    setCurrentUser(user)
    form.setFieldsValue(user)
    setModalVisible(true)
  }

  const handleView = (user: User) => {
    setModalType("view")
    setCurrentUser(user)
    setModalVisible(true)
  }

  const handleDelete = (user: User) => {
    Modal.confirm({
      title: "确认删除该用户？",
      content: `用户名：${user.name}`,
      onOk: async () => {
        try {
          await deleteUser(user.id)
          message.success("删除用户成功")
          getUserList() // 重新获取用户列表
        } catch (error) {
          console.error("删除用户失败:", error)
          message.error("删除用户失败")
        }
      },
    })
  }

  const handleSubmit = async () => {
    try {
      setSubmitting(true)
      const values = await form.validateFields()

      if (modalType === "add") {
        const createParams: CreateUserParams = {
          account: values.account,
          department: values.department || "",
          password: values.password,
          roleId: Number(values.roleId),
          unit: values.unit || "",
          email: values.email,
          enable: values.status,
          name: values.name,
        }

        await createUser(createParams)
        message.success("新增用户成功")
        setModalVisible(false)
        getUserList() // 重新获取用户列表
      } else if (modalType === "edit" && currentUser) {
        const updateParams: UpdateUserParams = {
          id: currentUser.id,
          ...values,
        }

        await updateUser(updateParams)
        message.success("编辑用户成功")
        setModalVisible(false)
        getUserList() // 重新获取用户列表
      }
    } catch (error) {
      console.error("操作失败:", error)
      message.error("操作失败")
    } finally {
      setSubmitting(false)
    }
  }
  const filterUsers = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const value = e.target.value
    if (value === "全部单位") {
      //   setUnitFilter(undefined)
    } else {
      //   setUnitFilter(value)
    }
  }
  const handleResetPassword = (user: User) => {
    Modal.confirm({
      title: "确认重置该用户的密码？",
      content: `用户名：${user.name}`,
      onOk: async () => {
        try {
          await resetPassword(user.id)
          message.success("重置密码成功")
        } catch (error) {
          console.error("重置密码失败:", error)
          message.error("重置密码失败")
        }
      },
    })
  }
  const handleToggleStatus = async (user: User) => {
    try {
      await toggleUserStatus(user.id)
      message.success(`${user.enable ? "禁用" : "启用"}用户成功`)
      getUserList() // 重新获取用户列表
    } catch (error) {
      console.error("切换用户状态失败:", error)
    }
  }

  const columns = [
    {
      title: "用户信息",
      dataIndex: "name",
      render: (_: any, record: any) => (
        <div className="flex items-center">
          {/* <img src={record.avatar} alt={record.name} className="mr-2 h-8 w-8 rounded-full" /> */}
          <div>
            <div className="font-medium">{record.name}</div>
            <div className="text-xs text-gray-500">{record.email}</div>
          </div>
        </div>
      ),
    },
    {
      title: "单位/部门",
      render: (_: any, record: any) => `${record.unit}/${record.department}`,
    },
    {
      title: "角色",
      dataIndex: "roleName",
      render: (role: string) => {
        const color = role === "系统管理员" ? "purple" : role === "部门主管" ? "blue" : "green"
        return <Tag color={color}>{role}</Tag>
      },
    },
    {
      title: "权限范围",
      dataIndex: "permissionScope",
    },
    {
      title: "状态",
      dataIndex: "enable",
      render: (status: boolean) =>
        status === true ? <Tag color="green">启用</Tag> : <Tag color="red">禁用</Tag>,
    },
    {
      title: "操作",
      render: (_: any, record: User) => (
        <Space>
          <Button
            size="small"
            type="text"
            className="text-primary hover:text-primary/80 text-sm"
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          <Button
            size="small"
            type="text"
            className="ml-2 text-sm text-yellow-600 hover:text-yellow-700"
            onClick={() => handleResetPassword(record)}
          >
            重置密码
          </Button>
          <Button
            size="small"
            type="text"
            className={`ml-2 text-sm ${record.enable === true ? "text-red-600 hover:text-red-700" : "text-green-600 hover:text-green-700"}`}
            onClick={() => handleToggleStatus(record)}
          >
            {record.enable === true ? "禁用" : "启用"}
          </Button>
          <Button
            size="small"
            type="text"
            className="ml-2 text-sm text-red-600 hover:text-red-700"
            onClick={() => handleDelete(record)}
          >
            删除
          </Button>
        </Space>
      ),
    },
  ]

  return (
    <div className="border-border card-shadow mb-8 rounded-xl border bg-white p-6 shadow-sm">
      <div className="mb-6 flex items-center justify-between">
        <h3 className="text-xl font-bold text-gray-800">用户权限管理</h3>
        <Button
          className="bg-primary hover:bg-primary/90 flex items-center rounded-lg px-4 py-2 text-sm font-medium text-white transition-colors duration-200"
          onClick={handleAdd}
          icon={<PlusOutlined />}
          type="primary"
        >
          添加用户
        </Button>
      </div>
      <div className="mb-4 grid grid-cols-1 gap-4 md:grid-cols-3">
        {/* 单位筛选 */}
        <div>
          <label className="mb-2 block text-sm font-medium text-gray-700">单位筛选</label>
          <Select
            placeholder="选择单位"
            className="w-full"
            onChange={v => setUnitFilter(v)}
            options={unitOptions}
            allowClear
          />
        </div>

        {/* 部门筛选 */}
        <div>
          <label className="mb-2 block text-sm font-medium text-gray-700">部门筛选</label>
          <Select
            placeholder="选择部门"
            className="w-full"
            onChange={v => setDepartmentFilter(v)}
            options={departmentOptions}
            allowClear
          />
        </div>

        {/* 角色筛选 */}
        <div>
          <label className="mb-2 block text-sm font-medium text-gray-700">角色筛选</label>
          <Select
            placeholder="选择角色"
            className="w-full"
            onChange={v => setRoleFilter(v)}
            options={roleOptions}
            allowClear
          />
        </div>
      </div>

      <Table
        rowKey="id"
        dataSource={users}
        columns={columns}
        loading={loading}
        pagination={false}
      />

      <div className="flex items-center justify-end border-t border-neutral-200 bg-white px-4 py-3">
        <Pagination
          current={pagination.currentPage}
          total={pagination.total}
          pageSize={pagination.pageSize}
          showSizeChanger
          showTotal={total => `共 ${total} 条记录`}
          onChange={handlePaginationChange}
          onShowSizeChange={handlePaginationChange}
        />
      </div>

      <Modal
        title={modalType === "add" ? "新增用户" : modalType === "edit" ? "编辑用户" : "查看用户"}
        open={modalVisible}
        confirmLoading={submitting}
        onCancel={() => setModalVisible(false)}
        onOk={modalType === "view" ? undefined : handleSubmit}
        okButtonProps={{ disabled: modalType === "view" }}
        styles={{
          body: {
            height: 400, // 固定高度
            overflowY: "auto", // 超出时滚动
          },
        }}
      >
        <Form form={form} layout="vertical">
          <Form.Item name="name" label="用户名" rules={[{ required: true }]}>
            <Input disabled={modalType === "view"} />
          </Form.Item>
          {modalType === "add" && (
            <Form.Item name="account" label="账号" rules={[{ required: true }]}>
              <Input />
            </Form.Item>
          )}
          {modalType === "add" && (
            <Form.Item name="password" label="密码" rules={[{ required: true }]}>
              <Input />
            </Form.Item>
          )}
          <Form.Item name="email" label="邮箱" rules={[{ required: true, type: "email" }]}>
            <Input disabled={modalType === "view"} />
          </Form.Item>
          <Form.Item name="unit" label="单位" rules={[{ required: true }]}>
            <Select disabled={modalType === "view"} options={unitOptions} />
          </Form.Item>
          <Form.Item name="department" label="部门" rules={[{ required: true }]}>
            <Select disabled={modalType === "view"} options={departmentOptions} />
          </Form.Item>
          <Form.Item name="roleId" label="角色" rules={[{ required: true }]}>
            <Select disabled={modalType === "view"} options={roleOptions} />
          </Form.Item>
          {/* <Form.Item name="modules" label="权限范围" rules={[{ required: true }]}>
            <Select disabled={modalType === "view"}>
              <Select.Option value="全部模块">全部模块</Select.Option>
              <Select.Option value="知识库、问答、PPT">知识库、问答、PPT</Select.Option>
              <Select.Option value="问答、PPT">问答、PPT</Select.Option>
            </Select>
          </Form.Item> */}
          {/* <Form.Item name="enable" label="状态" rules={[{ required: true }]}>
            <Select disabled={modalType === "view"}>
              <Select.Option value={true}>启用</Select.Option>
              <Select.Option value={false}>禁用</Select.Option>
            </Select>
          </Form.Item> */}
        </Form>
      </Modal>
    </div>
  )
}

export default memo(UserManagement)
