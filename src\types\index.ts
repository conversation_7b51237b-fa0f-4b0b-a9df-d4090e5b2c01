// API 响应基础类型
export interface ApiResponse<T = any> {
  code: number;
  msg: string;
  data: T;
}

// 登录请求参数
export interface LoginRequest {
  account: string;
  password: string;
}

// 用户角色
export interface UserRole {
  code: string;
  name: string;
}

// 操作日志
export interface OperationLog {
  id: number;
  createId: number;
  createTime: string;
  addr: string;
  appellation: string;
  description: string;
  func: string;
  isException: boolean;
  lineNumber: number;
  link: string;
  loc: string;
  mod: string;
  msg: string;
  params: string;
  reqMethod: string;
  res: string;
  ty: string;
  uName: string;
  user: number;
}

// 操作日志查询参数
export interface QueryOperationLogParams {
  current: number;
  size: number;
  startTime?: string;
  endTime?: string;
  module?: string;
  type?: string;
  userName?: string;
}

// 操作日志列表响应
export interface OperationLogListResponse {
  current: number;
  next: number;
  pages: number;
  prev: number;
  records: OperationLog[];
  size: number;
  total: number;
}

// 角色信息
export interface Role {
  id: number;
  name: string;
  description: string;
  permissionIds?: number[];
  createTime?: string;
  updateTime?: string;
}

// 角色查询参数
export interface QueryRoleParams {
  current: number;
  size: number;
  id?: number;
  pageable: boolean;
  queryType?: string;
  userId?: number;
}

// 角色列表响应
export interface RoleListResponse {
  records: Role[];
  total?: number;
  current?: number;
  size?: number;
}

// 创建角色参数
export interface CreateRoleParams {
  name: string;
  description: string;
  permissionIds: number[];
}

// 更新角色参数
export interface UpdateRoleParams {
  id: number;
  name?: string;
  description?: string;
  permissionIds?: number[];
}

// 选项类型（用于下拉框）
export interface OptionItem {
  id: number;
  name: string;
  value: string;
  label: string;
  key: string;
  detail: string;
}

// 用户信息
export interface UserInfo {
  id: string;
  userName: string;
  name: string;
  avatar?: string;
}

// 权限菜单项
export interface Permission {
  id: string;
  parentId: string;
  name: string;
  code: string;
  type: string; // "1" 表示有权限，"2" 表示无权限
  url: string;
  icon: string | null;
  sort: number;
  children?: Permission[];
}

// 权限响应数据
export type PermissionsResponse = Permission[];

// 登录响应数据（token）
export type LoginResponse = string;

// 路由配置
export interface RouteConfig {
  path: string;
  name: string;
  component: React.ComponentType;
  requireAuth?: boolean;
  children?: RouteConfig[];
}

// 权限树节点
export interface PermissionTreeNode {
  id: string;
  createId: string;
  createTime: string;
  code: string;
  type: number;
  component: string | null;
  pId: string;
  label: string;
  url: string;
  img: string | null;
  order: number;
  ena: boolean;
  show: boolean | null;
  init: boolean | null;
  pChild?: PermissionTreeNode[];
}
