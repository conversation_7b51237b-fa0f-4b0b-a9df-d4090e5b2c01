.login-page {
  width: 1920px;
  height: 1080px;
  transform-origin: left top;
  overflow: hidden;
  background: #f7fbfd;
}
.login-container {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  width: 100%;
  padding: 0 10.8rem;
}

.login-content {
  display: flex;
  width: 100%;
  min-height: 600px;
  border-radius: 20px;
  overflow: hidden;

  box-shadow: 0px 4px 4px 0px #00000040;
}

.login-left {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: #ecf1fa;
  padding: 62px 58px 39px 62px;
}

.system-intro {
  margin-bottom: 52px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.system-title {
  font-size: 32px;
  font-weight: 500;
  color: #000;
  margin-bottom: 30px;
  line-height: 1.2;
}
.color1 {
  color: rgba(30, 116, 207, 0.9);
}
.color2 {
  color: rgba(49, 104, 214, 0.9);
}
.color3 {
  color: rgba(79, 85, 223, 0.9);
}
.color4 {
  color: rgba(109, 66, 233, 0.9);
}

.system-description {
  font-size: 16px;
  color: #000;
  font-weight: 300;
  line-height: 1.6;
  margin: 0;
}

.feature-cards {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.feature-card {
  border: none;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;

  border-radius: 20px;
  background: #fcfdfe;
  box-shadow: 0 1px 20px 0 rgba(0, 0, 0, 0.1);
  padding: 20px;
  gap: 20px;
}

.feature-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
}

.feature-content {
  display: flex;

  flex-direction: column;

  gap: 20px;
}

.feature-icon {
  flex-shrink: 0;
}

.feature-text {
  flex: 1;
}
.icon {
  display: flex;
  width: 53px;
  height: 53px;
  padding: 6px 4px;
  justify-content: center;
  align-items: center;
  flex-shrink: 0;
  aspect-ratio: 1/1;
  border-radius: 10px;
}
.icon1 {
  background: #357bf4;
}
.icon2 {
  background: #1db957;
}
.icon3 {
  background: #973aed;
}
.icon4 {
  background: #f56b14;
}
.img1 {
  width: 24px;
  height: 24px;
}

.feature-title {
  color: #000;
  font-family: "HarmonyOS Sans SC";
  font-size: 24px;
  font-style: normal;
  font-weight: 700;
  line-height: normal;
}

.feature-description {
  color: #4b5563;
  font-family: "HarmonyOS Sans SC";
  font-size: 18px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
}

.login-right {
  flex: 0 0 40%;
  padding: 116px 65px 106px 58px;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(12px);
  /* background-image: url("@/assets/loginbg.svg"); */
  background: #fff;
  border-radius: 20px;
  border: 1px solid;
  border-image-source: linear-gradient(90deg, #105ef4 50%, #01a4ae 100%);
  background-size: 100% 100%;
  height: 762px;
}

.login-form-container {
  width: 100%;
  height: 100%;
}

.login-header {
  text-align: center;
  margin-bottom: 20px;
  display: flex;
  flex-direction: column;
}

.welcome-title {
  color: #000;
  font-family: "HarmonyOS Sans SC";
  font-size: 28px;
  font-style: normal;
  font-weight: 700;
  line-height: normal;
  text-align: start;
  margin-bottom: 14px;
}
.line {
  width: 61px;
  height: 4px;
  flex-shrink: 0;
  border-radius: 6px;
  background: linear-gradient(270deg, #865ef5 -1.64%, #0ea3e9 104.92%);
}

.welcome-subtitle {
  font-size: 14px;
  color: #5a6c7d;
  margin: 0;
}

.login-tabs .ant-tabs-nav {
  margin-bottom: 32px;
}

.login-tabs .ant-tabs-tab {
  font-size: 16px;
  font-weight: 500;
}

.login-input {
  height: 48px;
  border-radius: 8px;
  border: 1px solid #d9d9d9;
  transition: all 0.3s ease;
}

.login-input:hover,
.login-input:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
}

.login-button {
  display: flex;
  width: 328px;
  height: 52px;
  justify-content: center;
  align-items: center;
  gap: 10px;
  flex-shrink: 0;
  border-radius: 10px;
  background: linear-gradient(90deg, #199dea 0%, #7f63f5 100%);
}

.login-button:hover {
  background: linear-gradient(90deg, #199dea 0%, #7f63f5 100%) !important;
}
@media (max-height: 899px) {
  /* 1. 减小整体容器垂直间距 */

  /* 2. 缩小系统标题及下方间距 */
  .system-title {
    font-size: 28px; /* 原 32px */
    margin-bottom: 20px; /* 原 50px，减少标题下方空白 */
  }

  .feature-content {
    gap: 12px; /* 原 20px，减小卡片内图标与文字间距 */
  }
  .feature-title {
    font-size: 24px; /* 原 28px，缩小卡片标题 */
  }
  .feature-description {
    font-size: 16px; /* 原 18px，缩小描述文字 */
  }

  /* 4. 优化登录表单区域高度及间距 */
  .login-right {
    height: 600px; /* 原 762px，降低登录区高度 */
    padding: 40px 60px; /* 原 60px 上下内边距，减少垂直空间占用 */
  }
  .login-header {
    margin-bottom: 50px; /* 原 95px，大幅减少标题下方间距 */
  }
  .login-button {
    margin-top: 30px; /* 原 50px，减少按钮上方间距 */
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .login-content {
    flex-direction: column;
    max-width: 480px;
  }

  .login-left {
    padding: 40px 30px;
  }

  .feature-cards {
    grid-template-columns: 1fr;
  }

  .login-right {
    flex: none;
    padding: 40px 30px;
  }

  .system-title {
    font-size: 24px;
  }
}

.login-container .ant-tabs .ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn {
  color: #374151;
  font-family: "HarmonyOS Sans SC";
  font-size: 16px;
  font-style: normal;
  font-weight: 700;
  line-height: normal;
}
.login-container .ant-tabs .ant-tabs-ink-bar {
  background: #000000;
  height: 0px;
}
.login-container .ant-form-item .ant-form-item-label > label {
  color: #374151;
  font-family: "HarmonyOS Sans SC";
  font-size: 14px;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
}
.login-container .ant-card .ant-card-body {
  padding: 0;
  height: 100%;
}
.login-container .ant-btn-icon {
  width: 20px;
  height: 20px;
}
