import React, { useEffect } from "react"
import { Navigate, useLocation } from "react-router-dom"
import { Spin } from "antd"
import { useAuthStore } from "../../store/useAuthStore"

interface AuthGuardProps {
  children: React.ReactNode
}

const AuthGuard: React.FC<AuthGuardProps> = ({ children }) => {
  const location = useLocation()
  const { isAuthenticated, setLoading } = useAuthStore()

  useEffect(() => {
    // 检查token是否存在
    const token = localStorage.getItem("token")
    if (token && !isAuthenticated) {
      // 如果有token但store中未认证，需要初始化认证状态
      setLoading(false)
    }
  }, [isAuthenticated, setLoading])

  // 检查token是否存在
  const token = localStorage.getItem("token")

  // 未认证，重定向到登录页
  if (!token && !isAuthenticated) {
    return <Navigate to="/login" state={{ from: location }} replace />
  }

  // 已认证，渲染子组件
  return <>{children}</>
}

export default AuthGuard
