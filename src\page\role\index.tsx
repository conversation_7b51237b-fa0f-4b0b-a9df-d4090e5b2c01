import RoleManagement from "./components/RoleManagement"
import UserManagement from "./components/UserManagement"
import AuditLog from "./components/AuditLog"
import { useManagementData } from "../../hooks/useManagementData"

const ManagementPage = () => {
  // 使用自定义Hook获取数据和方法
  const managementData = useManagementData()

  return (
    <div className="container mx-auto px-4 py-8 md:px-6">
      <div className="mb-8">
        <h2 className="text-[clamp(1.5rem,3vw,2.5rem)] font-bold text-gray-800">权限管理</h2>
        <p className="mt-2 text-base text-[#6B7280]">管理、部署和监控大语言模型的核心平台</p>
      </div>

      <RoleManagement
        roles={managementData.roles}
        loading={managementData.roleLoading}
        pagination={managementData.rolePagination}
        setPagination={managementData.setRolePagination}
        permissionTree={managementData.permissionTree}
        roleOptions={managementData.roleOptions}
        getRoleList={managementData.getRoleList}
        createRole={managementData.createRole}
        updateRole={managementData.updateRole}
        deleteRole={managementData.deleteRole}
        fetchOptions={managementData.fetchOptions}
      />
      <UserManagement
        users={managementData.users}
        loading={managementData.userLoading}
        pagination={managementData.userPagination}
        setPagination={managementData.setUserPagination}
        roleFilter={managementData.roleFilter}
        setRoleFilter={managementData.setRoleFilter}
        unitFilter={managementData.unitFilter}
        setUnitFilter={managementData.setUnitFilter}
        departmentFilter={managementData.departmentFilter}
        setDepartmentFilter={managementData.setDepartmentFilter}
        roleOptions={managementData.roleOptions}
        departmentOptions={managementData.departmentOptions}
        unitOptions={managementData.unitOptions}
        getUserList={managementData.getUserList}
        createUser={managementData.createUser}
        updateUser={managementData.updateUser}
        deleteUser={managementData.deleteUser}
        resetPassword={managementData.resetPassword}
        toggleUserStatus={managementData.toggleUserStatus}
      />
      <AuditLog
        logs={managementData.logs}
        loading={managementData.logLoading}
        pagination={managementData.logPagination}
        setPagination={managementData.setLogPagination}
        search={managementData.logSearch}
        setSearch={managementData.setLogSearch}
        filters={managementData.logFilters}
        setFilters={managementData.setLogFilters}
        operationTypes={managementData.operationTypes}
        getLogList={managementData.getLogList}
        exportLog={managementData.exportLog}
        truncateAllLogs={managementData.truncateAllLogs}
      />
    </div>
  )
}

export default ManagementPage
