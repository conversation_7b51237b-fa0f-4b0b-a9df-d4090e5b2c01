.pmList {
  border-radius: 24px;
  /* background: linear-gradient(0deg, #fff 0%, rgba(255, 255, 255, 0.4) 100%);
  box-shadow: 0 4px 4px 0 rgba(119, 146, 185, 0.1); */
}
.dashboard {
  border-radius: 24px;
  border: 1px solid rgba(0, 0, 0, 0.2);
  background: #fff;
  box-shadow: 0 4px 4px 0 rgba(0, 0, 0, 0.25);
  height: 309px;
}
.dashboardItem {
  border-radius: 20px;
  background: #f7fbfd;
  box-shadow: 0 4px 4px 0 rgba(0, 0, 0, 0.25);
}

.uploadDatasetDragger,
.createTaskDragger {
  width: 100%;
  height: 300px;
  display: inline-block;
  margin-top: 1rem;
}

.uploadDatasetDragger {
  height: 239px;
}

.createTaskDraggerInner {
  transform: translateY(-40%);
}

.uploadDatasetDragger .ant-upload.ant-upload-drag,
.createTaskDragger .ant-upload.ant-upload-drag {
  border-radius: 16px;
  background-color: #19c1a308;
  border: 1px dashed #0fb698;
  box-sizing: border-box;
}
.upModal.ant-modal .ant-modal-content {
  background: linear-gradient(180deg, #e1f3fb 0%, #f3f7fb 13.54%, #f8fafc 100%);
  padding-left: 0;
  padding-right: 0;
  & > .ant-modal-header {
    padding: 0 20px;
    background: transparent;
  }
}
.upModal .ScrollbarsCustom-Wrapper {
  margin: 0 40px;
}

.stting-content .ant-collapse {
  background: transparent;
}
.stting-content .ant-collapse .ant-collapse-content {
  background: transparent;
  border: 1px solid #e1f3fb;
}
.stting-content
  .ant-collapse-large
  > .ant-collapse-item
  > .ant-collapse-content
  > .ant-collapse-content-box {
  padding: 24px 217px;
}
.stting-content .ant-form-item-horizontal .ant-form-item-control {
  margin-left: 24px;
}
.stting-content .ant-collapse > .ant-collapse-item > .ant-collapse-header {
  align-items: center;
}
.stting-content .ant-collapse {
  border: 1px solid #f3f7fb;
}
