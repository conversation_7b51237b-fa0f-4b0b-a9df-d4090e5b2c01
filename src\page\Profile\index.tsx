import React, { useState, useEffect } from "react"
import { Button, Form, Input, message, Space } from "antd"
import { <PERSON><PERSON>eftOutlined, EditOutlined } from "@ant-design/icons"
import { useNavigate } from "react-router-dom"
import { useAuthStore } from "../../store/useAuthStore"
import goback from "@/assets/goback.svg"

import "./index.css"

interface UserProfile {
  account: string
  roleName: string
  userName: string
  password: string
}

const Profile: React.FC = () => {
  const navigate = useNavigate()
  const { userInfo } = useAuthStore()
  const [form] = Form.useForm()
  const [loading, setLoading] = useState(false)
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null)
  const [isEditingUserName, setIsEditingUserName] = useState(false)
  const [isEditingPassword, setIsEditingPassword] = useState(false)

  useEffect(() => {
    // 从localStorage获取用户角色信息
    const storedUserRoles = localStorage.getItem("userRoles")
    if (storedUserRoles) {
      try {
        const userRoles = JSON.parse(storedUserRoles)
        if (userRoles && userRoles.length > 0) {
          const profile = userRoles[0]
          setUserProfile({
            account: profile.account || "",
            roleName: profile.roleName || "",
            userName: profile.userName || "",
            password: profile.password || "******",
          })
          form.setFieldsValue({
            userName: profile.userName || "",
            password: profile.password || "",
          })
        }
      } catch (error) {
        console.error("解析用户信息失败:", error)
        message.error("获取用户信息失败")
      }
    }
  }, [])

  const handleSave = async (field?: "userName" | "password") => {
    try {
      setLoading(true)

      // 验证特定字段或所有字段
      let fieldsToValidate: string[] = []
      if (field === "userName") {
        fieldsToValidate = ["userName"]
      } else if (field === "password") {
        fieldsToValidate = ["password"]
      } else {
        fieldsToValidate = ["userName", "password"]
      }

      const values = await form.validateFields(fieldsToValidate)

      // 更新localStorage中的用户信息
      const storedUserRoles = localStorage.getItem("userRoles")
      if (storedUserRoles) {
        const userRoles = JSON.parse(storedUserRoles)
        if (userRoles && userRoles.length > 0) {
          const updateData: any = { ...userRoles[0] }

          if (field === "userName" && values.userName && values.userName.trim()) {
            updateData.userName = values.userName.trim()
          } else if (field === "password" && values.password && values.password.trim()) {
            updateData.password = values.password.trim()
          } else if (!field) {
            // 保存所有字段
            if (values.userName && values.userName.trim()) {
              updateData.userName = values.userName.trim()
            }
            if (values.password && values.password.trim()) {
              updateData.password = values.password.trim()
            }
          }

          userRoles[0] = updateData
          localStorage.setItem("userRoles", JSON.stringify(userRoles))

          // 更新本地状态
          setUserProfile(prev =>
            prev
              ? {
                  ...prev,
                  ...updateData,
                }
              : null
          )

          if (field === "userName") {
            setIsEditingUserName(false)
          } else if (field === "password") {
            setIsEditingPassword(false)
          } else {
            setIsEditingUserName(false)
            setIsEditingPassword(false)
          }

          message.success("个人信息更新成功")
        }
      }
    } catch (error) {
      if (error.errorFields && error.errorFields.length > 0) {
        // 表单验证失败
        const firstError = error.errorFields[0]
        message.error(firstError.errors[0])
      } else {
        console.error("保存失败:", error)
        message.error("保存失败，请重试")
      }
    } finally {
      setLoading(false)
    }
  }

  const handleCancel = (field?: "userName" | "password") => {
    if (userProfile) {
      if (field === "userName") {
        form.setFieldValue("userName", userProfile.userName)
        setIsEditingUserName(false)
      } else if (field === "password") {
        form.setFieldValue("password", userProfile.password)
        setIsEditingPassword(false)
      } else {
        form.setFieldsValue({
          userName: userProfile.userName,
          password: userProfile.password,
        })
        setIsEditingUserName(false)
        setIsEditingPassword(false)
      }
    }
  }

  const handleKeyPress = (e: React.KeyboardEvent, field: "userName" | "password") => {
    if (e.key === "Enter") {
      handleSave(field)
    }
  }

  return (
    <div className="profile-container h-full px-20">
      <div className="mt-12">
        {/* 头部 */}
        <div className="mb-8 flex items-center gap-3">
          <Button
            icon={<img src={goback} alt="" />}
            onClick={() => navigate(-1)}
            className="flex items-center justify-center border-none shadow-none"
            type="text"
          />
          <h1 className="text-2xl font-medium text-gray-800">个人信息</h1>
        </div>

        {/* 基本信息卡片 */}
        <div className="profile-card">
          <div className="mb-6">
            <h2 className="text-lg font-bold text-black">基本信息</h2>
          </div>

          <Form form={form} layout="vertical" className="profile-form px-12">
            {/* 用户名 */}
            <div className="form-row">
              <div className="form-label">用户名：</div>
              <div className="form-value">
                {isEditingUserName ? (
                  <Form.Item
                    name="userName"
                    className="mb-0 flex-1"
                    rules={[
                      { required: true, message: "用户名不能为空" },
                      { whitespace: true, message: "用户名不能为空" },
                    ]}
                  >
                    <Input
                      className="edit-input"
                      placeholder="请输入用户名"
                      onKeyPress={e => handleKeyPress(e, "userName")}
                      autoFocus
                    />
                  </Form.Item>
                ) : (
                  <span className="value-text min-w-32">{userProfile?.userName || "未设置"}</span>
                )}
                {/* {isEditingUserName ? (
                  <div className="flex gap-2 ml-2">
                    <Button size="small" onClick={() => handleCancel('userName')}>取消</Button>
                    <Button size="small" type="primary" onClick={() => handleSave('userName')}>保存</Button>
                  </div>
                ) : ( */}
                <EditOutlined className="edit-icon" onClick={() => setIsEditingUserName(true)} />
                {/* )} */}
              </div>
            </div>

            {/* 用户账号 */}
            <div className="form-row">
              <div className="form-label">用户账号：</div>
              <div className="form-value">
                <span className="value-text">{userProfile?.account || "未设置"}</span>
              </div>
            </div>

            {/* 用户密码 */}
            <div className="form-row">
              <div className="form-label">用户密码：</div>
              <div className="form-value">
                {isEditingPassword ? (
                  <Form.Item
                    name="password"
                    className="mb-0 flex-1"
                    rules={[
                      { required: true, message: "密码不能为空" },
                      { whitespace: true, message: "密码不能为空" },
                    ]}
                  >
                    <Input.Password
                      className="edit-input"
                      placeholder="请输入密码"
                      onKeyPress={e => handleKeyPress(e, "password")}
                      autoFocus
                    />
                  </Form.Item>
                ) : (
                  <span className="value-text min-w-32">••••••••</span>
                )}
                {/* {isEditingPassword ? (
                  <div className="ml-2 flex gap-2">
                    <Button size="small" onClick={() => handleCancel("password")}>
                      取消
                    </Button>
                    <Button size="small" type="primary" onClick={() => handleSave("password")}>
                      保存
                    </Button>
                  </div>
                ) : ( */}
                <EditOutlined className="edit-icon" onClick={() => setIsEditingPassword(true)} />
                {/* )} */}
              </div>
            </div>

            {/* 用户角色 */}
            <div className="form-row">
              <div className="form-label">用户角色：</div>
              <div className="form-value">
                <span className="role-tag">{userProfile?.roleName || "未分配角色"}</span>
              </div>
            </div>
          </Form>
        </div>
      </div>
    </div>
  )
}

export default Profile
