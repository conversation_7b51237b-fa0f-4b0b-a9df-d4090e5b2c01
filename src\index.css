@tailwind base;
@tailwind components;
@tailwind utilities;

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html,
body {
  height: 100%;
  font-family:
    -apple-system, BlinkMacSystemFont, "Segoe UI", "PingFang SC", "Hiragino Sans GB",
    "Microsoft YaHei", "Helvetica Neue", Helvetica, Arial, sans-serif;
  font-size: 14px;
  line-height: 1.5;
  color: #333;
  background-color: #f5f5f5;
}

#root {
  height: 100%;
}

a {
  color: #1890ff;
  text-decoration: none;
}

a:hover {
  color: #40a9ff;
}

.ant-layout {
  background: transparent;
}

/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
/* 全局样式文件 */
.ScrollbarsCustom-Track {
  /* 横向滚动条轨道样式 */
  background-color: #f1f1f1;
  border-radius: 8px;
}

.ScrollbarsCustom-TrackY {
  /* 纵向滚动条轨道样式 */
  width: 6px !important;
  background-color: #f1f1f1;
  border-radius: 3px;
  margin-right: 2px;
}

/* 可以同时修改滚动条滑块样式 */
.ScrollbarsCustom-Thumb {
  background-color: #c1c1c1;
  border-radius: 3px;
  transition: background-color 0.2s;
}

.ScrollbarsCustom-Thumb:hover {
  background-color: #a8a8a8;
}

.delete-model .ant-modal-content {
  height: 226px;
  display: flex;
  flex-direction: column;
  justify-content: space-around;
  padding: 20px 40px;
  border-radius: 24px;
}
.delete-model .ant-modal-content .ant-modal-body {
  display: flex;
  justify-content: center;
  align-items: center;
}
.delete-model .ant-modal-content .ant-modal-footer {
  display: flex;
  justify-content: space-around;
  align-items: center;
  padding: 0 100px;
}
.modal-btn.ant-btn-default:not(:disabled):not(.ant-btn-disabled):hover {
  background: #111111 !important;
  width: 82px;
  height: 40px;
  border-radius: 59px;
  color: #ffffff;
  font-weight: 700;
  border-radius: 59px;
  border: none;
}
.modal-btn.ant-btn-default {
  border: none;
  width: 82px;
  height: 40px;
}
.upload-list-item {
  box-sizing: border-box;
  width: 100%;
  height: 48px;
  background: #ffffff;
  border: 1px solid #eeeeee;
  box-shadow: 0px 2px 4px rgba(119, 146, 185, 0.05);
  border-radius: 8px;
  display: inline-flex;
  justify-content: flex-start;
  align-items: center;
  padding: 0 1rem;
  margin-bottom: 0.5rem;
  margin-left: 0 !important;
  margin-right: 0 !important;
}
