import axios, { AxiosInstance, InternalAxiosRequestConfig, AxiosResponse } from 'axios'
import { message } from 'antd'
import { ApiResponse } from '../types'

// 创建axios实例
const request: AxiosInstance = axios.create({
  baseURL: '/xs-platform/api/v1',
  timeout: 100000,
  headers: {
    'Content-Type': 'application/json',
  },
})

// 请求拦截器
request.interceptors.request.use(
  (config: InternalAxiosRequestConfig) => {
    const token = localStorage.getItem('token')
    if (token && config.headers) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => {
    console.error('请求拦截器错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
request.interceptors.response.use(
  (response: AxiosResponse<ApiResponse>) => {
    // 🔹 特殊处理：如果是文件流直接返回
    const contentType = response.headers['content-type']
    if (contentType?.includes('application/octet-stream') || contentType?.includes('application/vnd.ms-excel') || contentType?.includes('application/pdf') 
     || contentType?.includes('application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
    ) {
      return response
    }

    const { data } = response
    // 检查业务状态码
    if (data.code === 200) {
      return response
    } else {
      message.error(data.msg || '请求失败')
      return Promise.reject(new Error(data.msg || '请求失败'))
    }
  },
  (error) => {
    if (error.response) {
      const { status, data } = error.response

      switch (status) {
        case 401:
          message.error('未授权，请重新登录')
          localStorage.removeItem('token')
          localStorage.removeItem('userInfo')
          window.location.href = '/login'
          break
        case 403:
          message.error('权限不足')
          break
        case 404:
          message.error('请求的资源不存在')
          break
        case 500:
          message.error(data?.msg || '服务器内部错误')
          break
        default:
          message.error(data?.msg || `请求失败 (${status})`)
      }
    } else if (error.request) {
      message.error('网络错误，请检查网络连接')
    } else {
      message.error('请求配置错误')
    }

    return Promise.reject(error)
  }
)

export default request
