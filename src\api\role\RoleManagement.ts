import request from '../../services/request';
import {
  ApiResponse,
  Role,
  QueryRoleParams,
  RoleListResponse,
  CreateRoleParams,
  UpdateRoleParams,
  OptionItem
} from '../../types';

// 查询角色列表
export const queryRoleApi = (data: QueryRoleParams): Promise<ApiResponse<RoleListResponse>> => {
  // 过滤掉空值参数
  const filteredData = Object.fromEntries(
    Object.entries(data).filter(([_, value]) =>
      value !== undefined && value !== null && value !== ''
    )
  );

  return request.post('/role/query', filteredData).then(res => res.data);
};

// 创建角色
export const createRoleApi = (data: CreateRoleParams): Promise<ApiResponse<null>> => {
  return request.post('/role/insert', data).then(res => res.data);
};

// 编辑角色
export const updateRoleApi = (data: UpdateRoleParams): Promise<ApiResponse<null>> => {
  return request.put('/role/update', data).then(res => res.data);
};

// 删除角色
export const deleteRoleApi = (id: number): Promise<ApiResponse<null>> => {
  return request.delete(`/role/delete/${id}`).then(res => res.data);
};

// 获取全部可用角色选项列表（下拉框）
export const getRoleOptionsApi = (): Promise<ApiResponse<OptionItem[]>> => {
  return request.get('/role/options').then(res => res.data);
};