import {
  Button,
  Form,
  Modal,
  Upload,
  UploadProps,
  message,
  Input,
  Select,
  DatePicker,
  ConfigProvider,
} from "antd"
import <PERSON><PERSON> from "antd/es/upload/Dragger"
import { useState } from "react"
import Scrollbar from "react-scrollbars-custom"
import UploadDatasetList from "./UploadDatasetList"
import { uploadDataset, deleteDataset } from "../../../api/score/dataset.ts"
import uploadcomputer from "@/assets/img/uploadcomputer.svg"
import { UploadFile } from "antd/es/upload/interface"
import locale from "antd/locale/zh_CN"
import dayjs from "dayjs"
import "dayjs/locale/zh-cn"
dayjs.locale("zh-cn")
const { TextArea } = Input
const { RangePicker } = DatePicker
const { Option } = Select

export interface UploadItem {
  file: UploadFile
  originalFile?: File // 保存原始文件对象
  dataSetId?: string
  tags?: string[]
  status?: "uploading" | "done" | "error"
  parseState?: string
  name?: string
}

interface UploadDatasetModalProp {
  visible: boolean
  OnClose: () => void
}

const UploadDatasetModal: React.FC<UploadDatasetModalProp> = ({ visible, OnClose }) => {
  const [uploadList, setUploadList] = useState<UploadItem[]>([])
  const [form] = Form.useForm()

  const clearState = () => {
    setUploadList([])
    form.resetFields()
  }

  const handleConfirm = async () => {
    try {
      await form.validateFields()
      // 表单校验通过，执行确认逻辑
      clearState()
      OnClose()
    } catch (error) {
      // 表单校验失败，不关闭弹窗
      console.log("表单校验失败:", error)
    }
  }

  const props: UploadProps = {
    name: "file",
    multiple: true,
    action: "/api/qa_generator/upload_data",
    accept: ".txt,.docx,.doc,.pdf,.png,.jpg,.jpeg,.mp3,.mp4",
    showUploadList: false,
    beforeUpload: file => {
      const allowedExtensions = [".txt", ".docx", ".doc", ".pdf", ".png", ".jpg", ".mp3", ".mp4"]
      const fileName = file.name.toLowerCase()
      const isValid = allowedExtensions.some(ext => fileName.endsWith(ext))

      if (!isValid) {
        message.error(
          `不支持的文件类型: ${file.name}。请上传以下类型: ${allowedExtensions.join(", ")}`
        )
        return Upload.LIST_IGNORE
      }

      return true
    },
    onDrop(e) {
      console.log("Dropped files", e.dataTransfer.files)
    },
    customRequest: options => {
      const file = options.file as UploadFile
      const uid = file.uid

      setUploadList(prev => [...prev, { file, status: "uploading", name: file.name }])

      uploadDataset({ importDataset: file })
        .then(res => {
          if (res.data?.code === 200) {
            setUploadList(prev =>
              prev.map(item =>
                item.file.uid === uid ? { ...item, status: "done", dataSetId: res.data.data } : item
              )
            )
            if (options.onSuccess) options.onSuccess(res, file as any)
          } else {
            setUploadList(prev =>
              prev.map(item => (item.file.uid === uid ? { ...item, status: "error" } : item))
            )
            if (options.onError) options.onError(res.data)
          }
        })
        .catch(err => {
          setUploadList(prev =>
            prev.map(item => (item.file.uid === uid ? { ...item, status: "error" } : item))
          )
          if (options.onError) options.onError(err)
        })
    },
  }

  return (
    <Modal
      title="新建项目"
      centered
      keyboard={false}
      maskClosable={false}
      width={870}
      open={visible}
      className="upModal"
      onOk={() => {
        clearState()
        OnClose()
      }}
      onCancel={() => {
        clearState()
        OnClose()
      }}
      styles={{ body: { height: "524px" } }}
      footer={[
        <div key="okBtnDiv" style={{ display: "flex", justifyContent: "center" }}>
          <Button
            type="primary"
            className="primary-btn boldText"
            style={{ width: "124px" }}
            onClick={handleConfirm}
          >
            确认
          </Button>
        </div>,
      ]}
    >
      <Scrollbar>
        <div className="rounded-3xl bg-white px-14 py-6" style={{ height: "100%" }}>
          <Form layout="vertical" form={form}>
            <div className="flex gap-5 px-2">
              <Form.Item
                label="项目名称"
                name="projectName"
                className="w-1/2"
                rules={[
                  { required: true, message: "请输入项目名称" },
                  { max: 15, message: "项目名称不能超过15个字符" },
                  { pattern: /^[a-zA-Z0-9\u4e00-\u9fa5]+$/, message: "不允许输入特殊字符" },
                ]}
              >
                <Input placeholder="请输入项目名称" maxLength={15} />
              </Form.Item>

              <Form.Item
                label="项目类型"
                name="projectType"
                className="w-1/2"
                rules={[{ required: true, message: "请选择项目类型" }]}
              >
                <Select placeholder="请选择项目类型">
                  <Option value="type1">类型1</Option>
                  <Option value="type2">类型2</Option>
                  <Option value="type3">类型3</Option>
                </Select>
              </Form.Item>
            </div>
            <div className="flex gap-5 px-2">
              <Form.Item
                label="项目参与人"
                name="projectParticipants"
                className="w-1/2"
                rules={[{ required: true, message: "请选择项目参与人" }]}
              >
                <Select placeholder="请选择项目参与人" mode="multiple">
                  <Option value="user1">用户1</Option>
                  <Option value="user2">用户2</Option>
                  <Option value="user3">用户3</Option>
                </Select>
              </Form.Item>

              <Form.Item label="项目评分人" className="w-1/2" name="projectEvaluator">
                <Select placeholder="请选择项目评分人" allowClear>
                  <Option value="evaluator1">评分人1</Option>
                  <Option value="evaluator2">评分人2</Option>
                  <Option value="evaluator3">评分人3</Option>
                </Select>
              </Form.Item>
            </div>
            <Form.Item
              label="任务时间"
              name="taskTime"
              className="px-2"
              rules={[{ required: true, message: "请选择任务时间" }]}
            >
              <ConfigProvider locale={locale}>
                <RangePicker style={{ width: "100%" }} />
              </ConfigProvider>
            </Form.Item>

            <Form.Item label="项目描述" className="px-2" name="projectDescription">
              <TextArea rows={4} placeholder="请输入项目描述" maxLength={6} />
            </Form.Item>
          </Form>
          <span>评分依据上传</span>
          <Dragger {...props} style={{ height: 239 }} className="uploadDatasetDragger px-2">
            <div className="createTaskDraggerInner reqularText">
              <img className="createTaskDraggerIcon" alt="" src={uploadcomputer} />
              <p>拖入您需要解析的本地数据集文档，或点击进行选择</p>
            </div>
            <div className="createTaskDraggerInfo reqularText">
              <label
                className="crateTaskDraggerLabel"
                style={{ left: "39%", transform: "translateX(-31%)" }}
              >
                支持解析的文档类型有txt、doc、docx、pdf、png、jpg、jpeg、mp3、mp4
              </label>
            </div>
          </Dragger>
          {/* <div style={{ height: "369px" }}>
            <Scrollbar> */}
          <UploadDatasetList
            fileList={uploadList}
            // onAddTag={(datasetId, tags) => {
            //   changeDataSetsTags({ dataSetId: datasetId, tags }).then((res) => {
            //     if (res?.data?.code === 0) {
            //       setUploadList((prev) =>
            //         prev.map((item) => (item.dataSetId === datasetId ? { ...item, tags } : item))
            //       );
            //     }
            //   });
            // }}
            onDelFile={uid => {
              const target = uploadList.find(item => item.file.uid === uid)
              if (!target) return

              setUploadList(prev => prev.filter(item => item.file.uid !== uid))

              if (target.status === "done" && target.dataSetId) {
                deleteDataset([target.dataSetId])
                  .then(res => {
                    if (res.data?.code === 200) {
                      message.success("删除成功")
                    } else {
                      message.error("删除失败")
                    }
                  })
                  .catch(() => message.error("删除失败"))
              }
            }}
            onReUpload={uid => {
              const target = uploadList.find(item => item.file.uid === uid)
              if (!target) return

              setUploadList(prev =>
                prev.map(item => (item.file.uid === uid ? { ...item, status: "uploading" } : item))
              )

              uploadDataset({ importDataset: target.file })
                .then(res => {
                  if (res.data?.code === 200) {
                    setUploadList(prev =>
                      prev.map(item =>
                        item.file.uid === uid
                          ? { ...item, status: "done", dataSetId: res.data.data }
                          : item
                      )
                    )
                  } else {
                    setUploadList(prev =>
                      prev.map(item =>
                        item.file.uid === uid ? { ...item, status: "error" } : item
                      )
                    )
                  }
                })
                .catch(() => {
                  setUploadList(prev =>
                    prev.map(item => (item.file.uid === uid ? { ...item, status: "error" } : item))
                  )
                })
            }}
          />
          {/* </Scrollbar>
          </div> */}
        </div>
      </Scrollbar>
    </Modal>
  )
}

export default UploadDatasetModal
