import React from "react"
import { But<PERSON> } from "antd"
import { DeleteOutlined, PlusOutlined } from "@ant-design/icons"

interface ActiveButtonProps {
  selectedRowKeys: React.Key[]
  onDeleteProject: () => void
  onCreateProject: () => void
}

const ActiveButton: React.FC<ActiveButtonProps> = ({
  selectedRowKeys,
  onDeleteProject,
  onCreateProject,
}) => {
  return (
    <div className="flex items-center space-x-4">
      {/* <Button size="large" onClick={onProjectAnalysis}>
        项目分析
      </Button> */}
      <Button
        size="large"
        onClick={onDeleteProject}
        disabled={selectedRowKeys.length === 0}
        icon={<DeleteOutlined />}
      >
        删除项目
      </Button>
      <Button type="primary" size="large" onClick={onCreateProject} icon={<PlusOutlined />}>
        新建项目
      </Button>
    </div>
  )
}

export default ActiveButton
