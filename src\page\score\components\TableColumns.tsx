import React from "react"
import { But<PERSON>, Dropdown, Space } from "antd"
import type { MenuProps, TableColumnsType } from "antd"
import type { ColumnType } from "antd/es/table"
import { MoreOutlined } from "@ant-design/icons"
import { PMItem, RoleCode, ProjectOperations, UserRoleType } from "../types"
import loadingIcon from "@/assets/loading.svg"
import doneIcon from "@/assets/done.svg"
import errorIcon from "@/assets/error.svg"

// 通用表格数据类型
export type TableDataType = PMItem

// 表格类型枚举
export enum TableType {
  PERFORMANCE_LEADERBOARD = "performance_leaderboard",
  PROJECT_MANAGEMENT = "project_management",
}

// 操作按钮配置类型
export interface ActionButtonConfig {
  key: string
  label: string
  onClick: (id: string, record?: PMItem) => void // 修改为支持传递record参数
  className?: string
  type?: "primary" | "more" // 新增类型字段，区分主要按钮和更多菜单中的按钮
}

// 状态渲染组件
const StatusRenderer = ({ status }: { status: string }) => {
  const colorMap: Record<string, string> = {
    已完成: "bg-[#F0F9EA] text-[#76BD49]",
    异常: "bg-[#FDEDEE] text-[#EB5C56]",
    进行中: "bg-[#FDF6E6] text-[#F5A623]",
    未开始: "bg-[#F5F5F5] text-[#999999]",
  }
  return (
    <span
      className={`flex w-[90px] items-center justify-center gap-2.5 rounded-md px-2 py-1 text-xs font-medium ${colorMap[status]}`}
    >
      <img
        src={status === "已完成" ? doneIcon : status === "异常" ? errorIcon : loadingIcon}
        alt={status}
      />
      <span className="text-base">{status}</span>
    </span>
  )
}

// 材料状态渲染组件
const MaterialStatusRenderer = ({ status }: { status: string }) => {
  const colorMap: Record<string, string> = {
    已上传: "bg-[#F0F9EA] text-[#76BD49]",
    未上传: "bg-[#FDEDEE] text-[#EB5C56]",
  }
  return (
    <span className={`rounded-md px-2 py-1 text-base font-medium ${colorMap[status]}`}>
      {status}
    </span>
  )
}
const BooleanStatusRenderer = ({ status }: { status: string }) => {
  const colorMap = {
    是: "bg-[#F0F9EA] text-[#76BD49]",
    否: "bg-[#FDEDEE] text-[#EB5C56]",
  }
  return (
    <div className="flex justify-center">
      <span
        className={`rounded-md px-2 py-1 text-base font-medium ${colorMap[status as keyof typeof colorMap]}`}
      >
        {status}
      </span>
    </div>
  )
}

// 评分依据上传状态渲染组件（使用对号和叉号）
const ScoringBasisStatusRenderer = ({ status }: { status: boolean }) => {
  return (
    <div className="flex justify-center">
      <span
        className={`font-mediu rounded-md px-2 py-1 text-base ${status ? "bg-[#F0F9EA] text-[#76BD49]" : "bg-[#FDEDEE] text-[#EB5C56]"}`}
      >
        {status ? "✓" : "✗"}
      </span>
    </div>
  )
}

// 公共列配置定义
const createCommonColumns = (
  onDetail?: (id: string, rowData?: PMItem) => void
): Record<string, ColumnType<PMItem>> => ({
  projectName: {
    title: "项目名称",
    dataIndex: "projectName",
    key: "projectName",
    render: (text: string, record: PMItem) => (
      <span
        className="cursor-pointer font-medium text-[#1777FF]"
        onClick={() => onDetail?.(record.id, record)}
      >
        {text}
      </span>
    ),
    ellipsis: true,
  },
  projectType: {
    title: "项目类型",
    dataIndex: "projectType",
    key: "projectType",
    width: "10%",
    ellipsis: true,
  },
  participants: {
    title: "参与人",
    dataIndex: "participants",
    key: "participants",
    ellipsis: true,
    render: (participants: string[] | string) => {
      if (!participants) return "-"
      if (Array.isArray(participants)) {
        return participants.join(", ")
      }
      return participants
    },
  },
  evaluator: {
    title: "评分人",
    dataIndex: "graderName",
    key: "graderName",
    width: "10%",
  },
  score: {
    title: "分数",
    dataIndex: "score",
    key: "score",
    render: (score: number | undefined) => <span className="font-medium">{score || "-"}</span>,
  },
  createdTime: {
    title: "创建时间",
    dataIndex: "createTime",
    key: "createTime",
    width: "10%",
  },
  startTime: {
    title: "开始时间",
    dataIndex: "startTime",
    key: "startTime",
  },
  endTime: {
    title: "结束时间",
    dataIndex: "completeTime",
    key: "completeTime",
    width: "10%",
  },
  scoringBasisUploaded: {
    title: "评分依据是否上传",
    dataIndex: "scoringBasisUploaded",
    key: "scoringBasisUploaded",
    render: (status: string) => <ScoringBasisStatusRenderer status={status === "是"} />,
  },
  isPublished: {
    title: "是否发布",
    dataIndex: "isPublished",
    key: "isPublished",
    width: "7%",
    render: (status: string) => <BooleanStatusRenderer status={status} />,
  },
  materialStatus: {
    title: "材料是否上传",
    dataIndex: "materialStatus",
    key: "materialStatus",
    render: (status: string) => <MaterialStatusRenderer status={status} />,
  },
  scoreStatus: {
    title: "评分状态",
    dataIndex: "scoreStatus",
    key: "scoreStatus",
    render: (status: string) => <StatusRenderer status={status} />,
  },
})

// 公共操作栏组件Props
interface ActionButtonsProps {
  record: PMItem
  buttons: ActionButtonConfig[]
}

const ActionButtons = ({ record, buttons }: ActionButtonsProps) => {
  // 分离主要按钮和更多菜单中的按钮
  const primaryButtons = buttons.filter(button => button.type !== "more")
  const moreButtons = buttons.filter(button => button.type === "more")

  // 创建更多菜单项
  const moreMenuItems: MenuProps["items"] = moreButtons.map(button => ({
    key: button.key,
    label: button.label,
    onClick: () => button.onClick(record.id, record),
    className: button.className,
  }))

  return (
    <Space size="small">
      {/* 主要按钮 */}
      {primaryButtons.map(button => (
        <Button
          key={button.key}
          type="link"
          size="small"
          className={button.className || "!text-[#409EFF]"}
          onClick={() => button.onClick(record.id, record)}
        >
          {button.label}
        </Button>
      ))}

      {/* 更多操作下拉菜单 */}
      {moreButtons.length > 0 && (
        <Dropdown menu={{ items: moreMenuItems }} placement="bottomRight" trigger={["click"]}>
          <Button type="link" size="small" className="!text-[#409EFF]">
            更多
          </Button>
        </Dropdown>
      )}
    </Space>
  )
}

// 操作列配置生成器
const createActionColumn = (buttons: ActionButtonConfig[], width?: string): ColumnType<PMItem> => ({
  title: "操作",
  key: "actions",
  width: width || "auto",
  render: (_: any, record: PMItem) => <ActionButtons record={record} buttons={buttons} />,
})

// 项目负责人表格列配置
export const getProjectManagerColumns = (
  operations: ProjectOperations,
  onDetail?: (id: string, rowData?: PMItem) => void
): TableColumnsType<PMItem> => {
  const CommonColumns = createCommonColumns(onDetail)
  return [
    CommonColumns.projectName,
    { ...CommonColumns.projectType },
    CommonColumns.participants,
    CommonColumns.evaluator,
    CommonColumns.createdTime,
    { ...CommonColumns.endTime },
    CommonColumns.scoringBasisUploaded,
    CommonColumns.isPublished,
    createActionColumn(
      [
        {
          key: "uploadScoringBasis",
          label: "上传评分依据",
          onClick: (id: string, record?: PMItem) => operations.onUploadScoringBasis?.(id, record),
          type: "primary",
        },
        {
          key: "publish",
          label: "发布",
          onClick: (id: string, record?: PMItem) => operations.onPublish?.(id, record),
          type: "primary",
        },
        {
          key: "analyze",
          label: "项目分析",
          onClick: (id: string, record?: PMItem) => operations.onAnalyze?.(id, record),
          type: "primary",
        },
        {
          key: "edit",
          label: "编辑",
          onClick: (id: string, record?: PMItem) => operations.onEdit?.(id, record),
          type: "more",
        },
        {
          key: "delete",
          label: "删除",
          onClick: (id: string, record?: PMItem) => operations.onDelete?.(id, record),
          className: "!text-[#EB5C56]",
          type: "more",
        },
      ],
      "18%"
    ),
  ]
}

// 评分人表格列配置
export const getEvaluatorColumns = (
  operations: ProjectOperations,
  onDetail?: (id: string, rowData?: PMItem) => void
): TableColumnsType<PMItem> => {
  const CommonColumns = createCommonColumns(onDetail)
  return [
    CommonColumns.projectName,
    { ...CommonColumns.projectType, width: undefined, ellipsis: undefined },
    { ...CommonColumns.participants, ellipsis: undefined },
    CommonColumns.createdTime,
    { ...CommonColumns.endTime, width: undefined },
    CommonColumns.materialStatus,
    CommonColumns.scoreStatus,
    createActionColumn([
      {
        key: "manualScore",
        label: "人工评分",
        onClick: (id: string) => operations.onManualScore?.(id),
      },
    ]),
  ]
}

// 普通用户表格列配置
export const getUserColumns = (
  operations: ProjectOperations,
  onDetail?: (id: string, rowData?: PMItem) => void
): TableColumnsType<PMItem> => {
  const CommonColumns = createCommonColumns(onDetail)
  return [
    CommonColumns.projectName,
    { ...CommonColumns.projectType, width: undefined, ellipsis: undefined },
    { ...CommonColumns.evaluator, width: undefined },
    CommonColumns.score,
    CommonColumns.startTime,
    { ...CommonColumns.endTime, width: undefined },
    CommonColumns.materialStatus,
    createActionColumn([
      {
        key: "uploadMaterial",
        label: "上传材料",
        onClick: (id: string) => operations.onUploadMaterial?.(id),
      },
      {
        key: "trace",
        label: "追溯",
        onClick: (id: string) => operations.onTrace?.(id),
      },
    ]),
  ]
}

// 根据角色获取表格列配置
export const getColumnsByRole = (
  role: UserRoleType,
  operations: ProjectOperations,
  onDetail?: (id: string, rowData?: PMItem) => void
): TableColumnsType<PMItem> => {
  switch (role) {
    case "project_manager":
      return getProjectManagerColumns(operations, onDetail)
    case "evaluator":
      return getEvaluatorColumns(operations, onDetail)
    case "user":
      return getUserColumns(operations, onDetail)
    default:
      return getUserColumns(operations, onDetail)
  }
}
